post dialog must be as large

Create view only dialog for brands
Create view only dialog for invoice
Create view only dialog for account companies

Remoev OPEN from hub-card

Brands filter must not exist for external customers.

Settings page doesnt work Cannot access 'clearUpdates' before initialization

I want some wrapper (or error boundary) which must cover pages and key components to allow refresh in case there is error.

Non-admin users still sett invite tab on manage participants.

external participants can NEVER be admins.

notifications for adder to chat must specify chat channel id to open.

notifications must work with websocket and not poling, its more efficient.

--- 
The access services definetely need simplification
Permission Level Control
Roles

App-level roles

AD<PERSON><PERSON> (global system administrator)

Hub-level roles (per Collaboration Hub)

ADMIN

REVIEWER

CONTENT_CREATOR

REVIEWER_AND_CONTENT_CREATOR

Entities and Permissions
1. Hub Participants Management

Create / Update / Delete

Allowed: App-level ADMIN, Hub-level ADMIN

Read

Allowed: Everyone in the hub

2. Post Management

Create

Allowed: App-level ADMIN, Hub-level ADMIN, CONTENT_CREATOR, REVIEWER_AND_CONTENT_CREATOR

Update / Delete

Allowed: Post creator, App-level ADMIN, Hub-level ADMIN

Read

Allowed: All hub participants, App-level ADMIN

3. Chat Management

Create

Allowed: All hub participants, App-level ADMIN

Update / Delete

Allowed: Chat creator, Hub-level ADMIN, App-level ADMIN

4. Brief Management

Create

Allowed: All hub participants, App-level ADMIN

Update / Delete

Allowed: Brief creator, Hub-level ADMIN, App-level ADMIN

5. Post Reviews

Review (approve/reject/comment)

Allowed: Only the designated reviewers for the post

Access Control Requirements

App-level ADMIN check

Single method for verifying app-level ADMIN privileges

Hub-level role check

Single method for checking a user’s role in the current hub

Creator ownership check

Per-entity method to confirm if the current user is the creator (for update/delete actions)

Multitenancy

Account-based users → always verify account_id

External invited users (via magic link) → no account_id, check by HubParticipant existence