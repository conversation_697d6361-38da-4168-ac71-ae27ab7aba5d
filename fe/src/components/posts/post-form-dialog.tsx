import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { TextareaWithEmoji } from "@/components/ui/textarea-with-emoji"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { useIsMobile } from "@/hooks/use-mobile"
import { useCreatePost, useUpdatePost, usePost } from "@/hooks/posts"
import { useNotifyReviewersPostEdit } from "@/hooks/posts/use-notify-reviewers-post-edit"
import { MediaUpload } from "./media-upload"
import { ReviewerMultiSelect } from "./reviewer-multi-select"
import { cn } from "@/lib/utils"

// Form validation schema - aligned with PostFormValues type
const postFormSchema = z.object({
  caption: z
    .string()
    .max(2200, "Caption cannot exceed 2200 characters")
    .optional()
    .or(z.literal("")),
  media_uris: z
    .array(z.string().url("Invalid media URL")),
  reviewer_notes: z
    .string()
    .max(1000, "Reviewer notes cannot exceed 1000 characters")
    .optional()
    .or(z.literal("")),
  reviewer_ids: z
    .array(z.number())
    .max(10, "Cannot assign more than 10 reviewers to a post"),
}).refine((data) => {
  // Post must have either caption or media content (or both)
  return (data.caption && data.caption.trim().length > 0) ||
         (data.media_uris && data.media_uris.length > 0)
}, {
  message: "Post must have either a caption or media content",
  path: ["caption"], // Show error on caption field
})

// Type definition aligned with schema
type PostFormValues = z.infer<typeof postFormSchema>

interface PostFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  hubId: number
  postId?: number | null // If provided, dialog is in edit mode
  onSuccess?: () => void
}

export function PostFormDialog({
  open,
  onOpenChange,
  hubId,
  postId = null,
  onSuccess
}: PostFormDialogProps) {
  const { t, keys } = useTranslations()
  const isMobile = useIsMobile()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showNotifyReviewersDialog, setShowNotifyReviewersDialog] = useState(false)
  const [postIdForNotification, setPostIdForNotification] = useState<number | null>(null)

  const isEditing = !!postId

  // Hooks for API operations
  const createPostMutation = useCreatePost()
  const updatePostMutation = useUpdatePost()
  const notifyReviewersMutation = useNotifyReviewersPostEdit()

  // Fetch existing post data if editing
  const { data: existingPost, isLoading: isLoadingPost } = usePost(postId, {
    enabled: isEditing && open
  })

  const form = useForm<PostFormValues>({
    resolver: zodResolver(postFormSchema),
    defaultValues: {
      caption: "",
      media_uris: [],
      reviewer_notes: "",
      reviewer_ids: [],
    },
  })

  // Reset form when dialog opens/closes or when switching between create/edit
  useEffect(() => {
    if (open) {
      if (isEditing && existingPost) {
        // Populate form with existing post data
        form.reset({
          caption: existingPost.caption || "",
          media_uris: existingPost.media_uris?.map(media => media.url || "") || [],
          reviewer_notes: existingPost.reviewer_notes || "",
          reviewer_ids: existingPost.assigned_reviewers?.map(reviewer => reviewer.id || 0).filter(id => id > 0) || [],
        })
      } else if (!isEditing) {
        // Reset form for new post
        form.reset({
          caption: "",
          media_uris: [],
          reviewer_notes: "",
          reviewer_ids: [],
        })
      }
    }
  }, [open, isEditing, existingPost, form])

  const handleSubmit = async (data: PostFormValues) => {
    setIsSubmitting(true)
    
    try {
      if (isEditing && postId) {
        // Check if post has existing reviews before update
        const hasExistingReviews = existingPost?.assigned_reviewers?.some(
          reviewer => reviewer.status === 'approved' || reviewer.status === 'rework'
        ) || false

        // Update existing post
        await updatePostMutation.mutateAsync({
          params: { path: { postId } },
          body: {
            caption: data.caption || undefined,
            media_uris: data.media_uris?.length ? data.media_uris : undefined,
            reviewer_notes: data.reviewer_notes || undefined,
            reviewer_ids: data.reviewer_ids?.length ? data.reviewer_ids : undefined,
          },
        })

        // If post had existing reviews, show confirmation dialog
        if (hasExistingReviews) {
          setPostIdForNotification(postId)
          setShowNotifyReviewersDialog(true)
        }
      } else {
        // Create new post
        await createPostMutation.mutateAsync({
          params: { path: { hubId } },
          body: {
            caption: data.caption || undefined,
            media_uris: data.media_uris?.length ? data.media_uris : undefined,
            reviewer_notes: data.reviewer_notes || undefined,
            reviewer_ids: data.reviewer_ids?.length ? data.reviewer_ids : undefined,
          },
        })
      }

      // Success - close dialog and notify parent (unless showing notification dialog)
      if (!isEditing || !existingPost?.assigned_reviewers?.some(
        reviewer => reviewer.status === 'approved' || reviewer.status === 'rework'
      )) {
        onOpenChange(false)
        onSuccess?.()
      }
      
    } catch (__error) {
      // Error handling is managed by the mutation hooks
      // Error handling for post save
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onOpenChange(false)
    }
  }

  const handleNotifyReviewers = async () => {
    if (!postIdForNotification) return

    try {
      await notifyReviewersMutation.mutateAsync({
        params: { path: { postId: postIdForNotification } }
      })
    } catch (error) {
      console.error('Failed to notify reviewers:', error)
    } finally {
      setShowNotifyReviewersDialog(false)
      setPostIdForNotification(null)
      onOpenChange(false)
      onSuccess?.()
    }
  }

  const handleSkipNotification = () => {
    setShowNotifyReviewersDialog(false)
    setPostIdForNotification(null)
    onOpenChange(false)
    onSuccess?.()
  }

  const dialogTitle = isEditing ? t(keys.collaborationHubs.posts.editPost) : t(keys.collaborationHubs.posts.createPost)
  const dialogDescription = isEditing
    ? t(keys.collaborationHubs.posts.editPostDescription)
    : t(keys.collaborationHubs.posts.createPostDescription)

  const submitButtonText = isSubmitting
    ? (isEditing ? t(keys.collaborationHubs.posts.form.updating) : t(keys.collaborationHubs.posts.form.creating))
    : (isEditing ? t(keys.collaborationHubs.posts.editPost) : t(keys.collaborationHubs.posts.createPost))

  // Show loading state while fetching existing post data
  if (isEditing && isLoadingPost) {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className={cn(
          "max-w-2xl",
          isMobile && "h-full max-h-screen w-full max-w-full rounded-none"
        )}>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>{t(keys.collaborationHubs.posts.loading)}</span>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <>
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className={cn(
        "max-w-4xl w-full",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0"
      )}>
        <DialogHeader className={cn(
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <DialogTitle className={cn(
            isMobile && "text-base"
          )}>{dialogTitle}</DialogTitle>
          <DialogDescription className={cn(
            isMobile && "text-xs"
          )}>
            {dialogDescription}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className={cn(
          "flex-1 mt-4",
          isMobile ? "h-[calc(100dvh-160px)] px-4" : "max-h-[70vh]"
        )}>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className={cn(
              "space-y-6 px-1",
              isMobile && "px-0"
            )}>
              
              {/* Media Upload */}
              <FormField
                control={form.control}
                name="media_uris"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t(keys.collaborationHubs.posts.form.media)}</FormLabel>
                    <FormControl>
                      <MediaUpload
                        value={field.value || []}
                        onChange={field.onChange}
                        hubId={hubId}
                        maxFiles={10}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      {t(keys.collaborationHubs.posts.form.mediaDescription)}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Caption */}
              <FormField
                control={form.control}
                name="caption"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t(keys.collaborationHubs.posts.form.caption)}</FormLabel>
                    <FormControl>
                      <TextareaWithEmoji
                        placeholder={t(keys.collaborationHubs.posts.form.captionPlaceholder)}
                        className="min-h-[100px] resize-none"
                        value={field.value || ""}
                        onChange={field.onChange}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      {field.value?.length || 0} / 2200 {t(keys.collaborationHubs.posts.form.captionDescription)}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Reviewer Notes */}
              <FormField
                control={form.control}
                name="reviewer_notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t(keys.collaborationHubs.posts.form.reviewerNotes)}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t(keys.collaborationHubs.posts.form.reviewerNotesPlaceholder)}
                        className="min-h-[80px] resize-none"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      {t(keys.collaborationHubs.posts.form.reviewerNotesDescription)} {field.value?.length || 0} / 1000 {t(keys.collaborationHubs.posts.form.captionDescription)}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

                <FormField
                  control={form.control}
                  name="reviewer_ids"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t(keys.collaborationHubs.posts.form.assignReviewers)}</FormLabel>
                      <FormControl>
                        <ReviewerMultiSelect
                          hubId={hubId}
                          value={field.value || []}
                          onChange={field.onChange}
                          disabled={isSubmitting}
                          placeholder={t(keys.ui.reviewerMultiSelect.selectReviewers)}
                        />
                      </FormControl>
                      <FormDescription>
                        {t(keys.collaborationHubs.posts.form.assignReviewersDescription)}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

              {/* Form-level validation error */}
              {form.formState.errors.root && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    {form.formState.errors.root.message}
                  </AlertDescription>
                </Alert>
              )}

            </form>
          </Form>
        </ScrollArea>

        <DialogFooter className={cn(
          "gap-2",
          isMobile && "pt-2 pb-4 px-4"
        )}>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            {t(keys.common.cancel)}
          </Button>
          <Button
            type="submit"
            onClick={form.handleSubmit(handleSubmit)}
            disabled={isSubmitting}
          >
            {isSubmitting && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
            {submitButtonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Confirmation dialog for notifying reviewers about post edits */}
    <AlertDialog open={showNotifyReviewersDialog} onOpenChange={setShowNotifyReviewersDialog}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {t(keys.collaborationHubs.posts.notifyReviewersTitle)}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {t(keys.collaborationHubs.posts.notifyReviewersDescription)}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleSkipNotification}>
            {t(keys.collaborationHubs.posts.noNotifyReviewers)}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleNotifyReviewers}
            disabled={notifyReviewersMutation.isPending}
          >
            {notifyReviewersMutation.isPending && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
            {t(keys.collaborationHubs.posts.yesNotifyReviewers)}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </>
  )
}
