import { useEffect, useR<PERSON>, use<PERSON><PERSON>back, useMemo, useState } from "react"
import { <PERSON>ader2, AlertCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { PostCard } from "./post-card"
import { PostCardSkeleton } from "./post-card-skeleton"
import { PostFilterChips } from "./post-filter-chips"
import { usePostsInfinite } from "@/hooks/posts/use-posts-infinite"
import { usePostsFilter } from "@/hooks/posts/use-post-filters"
import { useMediaUrlRefresh } from "@/hooks/media/use-media-url-refresh"
import type { PostFilterType } from "./post-filter-chips"
import { ComponentErrorBoundary } from "@/components/error-boundary"

interface PostsListProps {
  hubId: number
  searchQuery?: string
  onEditPost?: (postId: number) => void
  className?: string
  showFilters?: boolean
}

// Helper function to get display names for filters
function getFilterDisplayName(filter: PostFilterType): string {
  switch (filter) {
    case 'assigned_to_me': return 'Assigned to Me'
    case 'needs_review': return 'Needs Review'
    case 'my_pending': return 'My Pending'
    case 'my_approved': return 'My Approved'
    case 'my_rework': return 'My Rework'
    case 'reviewed_by_me': return 'Reviewed by Me'
    default: return 'All Posts'
  }
}

function PostsListInternal({
  hubId,
  searchQuery,
  onEditPost,
  className,
  showFilters = true
}: PostsListProps) {
  const { t, keys } = useTranslations()
  const loadMoreRef = useRef<HTMLDivElement>(null)
  const { scheduleUrlRefresh } = useMediaUrlRefresh()
  // State for React Query to track filter changes
  const [apiFilter, setApiFilter] = useState<PostFilterType>('all')
  const currentFilterRef = useRef<PostFilterType>('all')

  // Event-driven filter system - minimal re-renders
  const { onFilterChange } = usePostsFilter()

  // Use infinite posts hook with pagination (React Query tracks apiFilter changes)
  const {
    data,
    isLoading,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch
  } = usePostsInfinite(hubId, apiFilter, undefined, 20, {
    enabled: !!hubId,
    staleTime: 3 * 60 * 1000 // 3 minutes - safe margin for URL expiration
  })

  // Subscribe to filter changes and update React Query state
  useEffect(() => {
    onFilterChange((newFilter) => {
      if (currentFilterRef.current !== newFilter) {
        currentFilterRef.current = newFilter
        setApiFilter(newFilter) // This triggers React Query refetch automatically
      }
    })
  }, [onFilterChange])

  // Flatten all pages of posts and filter out any undefined values
  const allPosts = useMemo(() =>
    data?.pages?.flatMap(page => page.content).filter(Boolean) ?? [],
    [data?.pages]
  )

  // Schedule URL refresh when posts are loaded
  useEffect(() => {
    if (allPosts.length > 0 && hubId) {
      const hasMedia = allPosts.some(post => post?.media_uris && post.media_uris.length > 0)
      if (hasMedia) {
        scheduleUrlRefresh(hubId, undefined, 10) // 10 minutes for mixed content
      }
    }
  }, [allPosts, hubId, scheduleUrlRefresh])

  // Intersection Observer for infinite scroll
  const handleIntersection = useCallback((entries: IntersectionObserverEntry[]) => {
    const [entry] = entries
    if (entry.isIntersecting && hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage])

  useEffect(() => {
    const observer = new IntersectionObserver(handleIntersection, {
      threshold: 0.1,
      rootMargin: '100px'
    })

    const currentRef = loadMoreRef.current
    if (currentRef) {
      observer.observe(currentRef)
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef)
      }
    }
  }, [handleIntersection])

  // Filter posts by search query (client-side filtering for now)
  const filteredPosts = searchQuery
    ? allPosts.filter(post =>
        post && (
          post.caption?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          post.creator?.name?.toLowerCase().includes(searchQuery.toLowerCase())
        )
      )
    : allPosts

  // Loading state
  if (isLoading) {
    return (
      <div className={className}>
        <div className="max-w-lg mx-auto space-y-6">
          {/* Filter Chips */}
          {showFilters && (
            <PostFilterChips className="mb-6" />
          )}

          {Array.from({ length: 3 }).map((_, index) => (
            <PostCardSkeleton key={index} />
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (isError) {
    return (
      <div className={className}>
        <div className="max-w-lg mx-auto space-y-6">
          {/* Filter Chips */}
          {showFilters && (
            <PostFilterChips className="mb-6" />
          )}

          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>
                {t(keys.collaborationHubs.posts.error)}
                <br />
                <span className="text-sm opacity-80">
                  {t(keys.collaborationHubs.posts.errorDescription)}
                </span>
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                className="ml-4"
              >
                {t(keys.collaborationHubs.posts.retry)}
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    )
  }

  // Empty state
  if (filteredPosts.length === 0) {
    return (
      <div className={className}>
        <div className="max-w-lg mx-auto space-y-6">
          {/* Filter Chips */}
          {showFilters && (
            <PostFilterChips className="mb-6" />
          )}

          <div className="text-center py-12">
            <div className="mb-4">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-muted-foreground"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {currentFilterRef.current !== 'all'
                  ? `No posts found for "${getFilterDisplayName(currentFilterRef.current)}" filter`
                  : searchQuery
                    ? `No posts found for "${searchQuery}"`
                    : t(keys.collaborationHubs.posts.noPosts)
                }
              </h3>
              <p className="text-muted-foreground mb-6">
                {currentFilterRef.current !== 'all'
                  ? "Try selecting a different filter to see more posts."
                  : searchQuery
                    ? "Try adjusting your search terms or filters."
                    : t(keys.collaborationHubs.posts.noPostsDescription)
                }
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="max-w-lg mx-auto space-y-6">
        {/* Filter Chips */}
        {showFilters && (
          <PostFilterChips className="mb-6" />
        )}

        {/* Posts */}
        {filteredPosts.filter(post => post != null).map((post) => (
          <PostCard
            key={post.id}
            post={post}
            hubId={hubId}
            onEdit={onEditPost}
          />
        ))}

        {/* Load More Trigger */}
        <div ref={loadMoreRef} className="flex justify-center py-4">
          {isFetchingNextPage && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              {t(keys.collaborationHubs.posts.loadingMore)}
            </div>
          )}
        </div>

        {/* End of list indicator */}
        {!hasNextPage && filteredPosts.length > 0 && (
          <div className="text-center py-4">
            <p className="text-sm text-muted-foreground">
              You've reached the end of the posts
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

/**
 * PostsList component wrapped with error boundary for better error handling.
 * Isolates errors to prevent them from crashing the entire page.
 */
export function PostsList(props: PostsListProps) {
  return (
    <ComponentErrorBoundary
      componentName="PostsList"
      isolate={true}
      fallbackType="component"
    >
      <PostsListInternal {...props} />
    </ComponentErrorBoundary>
  )
}
