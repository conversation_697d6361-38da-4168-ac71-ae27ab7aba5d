import React from 'react';
import { Building2, Mail, Phone, MapPin, Globe, FileText, Edit, Loader2, AlertCircle, MoreHorizontal, Trash2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useAccountCompany } from '@/hooks/account-companies';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useIsMobile } from '@/hooks/use-mobile';
import { usePermissions } from '@/hooks/use-permissions';
import { cn } from '@/lib/utils';
import type { AccountCompany } from './types';

interface CompanyViewDialogProps {
  companyId: number | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (company: AccountCompany) => void;
  onDelete?: (company: AccountCompany) => void;
}

export const CompanyViewDialog = React.memo(function CompanyViewDialog({
  companyId,
  open,
  onOpenChange,
  onEdit,
  onDelete
}: CompanyViewDialogProps) {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();
  const { canManageCompanies } = usePermissions();

  // Fetch company data when dialog opens
  const {
    data: company,
    isLoading,
    isError
  } = useAccountCompany(companyId || undefined, open && !!companyId);

  const handleEdit = React.useCallback(() => {
    if (company && onEdit) {
      onEdit(company);
    }
  }, [company, onEdit]);

  const handleDelete = React.useCallback(() => {
    if (company && onDelete) {
      onDelete(company);
    }
  }, [company, onDelete]);

  const formatAddress = React.useMemo(() => {
    if (!company) return null;
    
    const parts = [
      company.address_street,
      company.address_city,
      company.address_postal_code,
      company.address_country
    ].filter(Boolean);
    
    return parts.length > 0 ? parts.join(', ') : null;
  }, [company]);

  if (!open) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn(
        "max-w-6xl max-h-[90vh] w-full",
        isMobile && "max-w-[95vw] h-[95vh]"
      )}>
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5 text-primary" />
            {isLoading ? (
              <span>Company Details</span>
            ) : (
              <span>{company?.company_name || 'Company Details'}</span>
            )}
          </DialogTitle>

          {canManageCompanies() && company && !isLoading && (
            <DropdownMenu>
              <DropdownMenuTrigger>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  {t(keys.common.edit)}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleDelete}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {t(keys.common.delete)}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </DialogHeader>

        <ScrollArea className="flex-1 pr-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : isError ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Error loading company data
              </AlertDescription>
            </Alert>
          ) : company ? (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Company Name
                    </label>
                    <p className="text-sm font-medium">{company.company_name || '-'}</p>
                  </div>

                  {(company.vat_number || company.registration_number) && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          VAT Number
                        </label>
                        <p className="text-sm">{company.vat_number || '-'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          Registration Number
                        </label>
                        <p className="text-sm">{company.registration_number || '-'}</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Email</label>
                        {company.email ? (
                          <a
                            href={`mailto:${company.email}`}
                            className="text-sm hover:text-primary transition-colors block"
                          >
                            {company.email}
                          </a>
                        ) : (
                          <p className="text-sm">-</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Phone</label>
                        {company.phone ? (
                          <a
                            href={`tel:${company.phone}`}
                            className="text-sm hover:text-primary transition-colors block"
                          >
                            {company.phone}
                          </a>
                        ) : (
                          <p className="text-sm">-</p>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Website</label>
                      {company.website ? (
                        <a
                          href={company.website.startsWith('http') ? company.website : `https://${company.website}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm hover:text-primary transition-colors block"
                        >
                          {company.website}
                        </a>
                      ) : (
                        <p className="text-sm">-</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Address</label>
                      {formatAddress ? (
                        <p className="text-sm">{formatAddress}</p>
                      ) : (
                        <p className="text-sm">-</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Metadata */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Metadata
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <label className="text-muted-foreground">
                        Created At
                      </label>
                      <p>{new Date(company.created_at).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <label className="text-muted-foreground">
                        Updated At
                      </label>
                      <p>{new Date(company.updated_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : null}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
});
