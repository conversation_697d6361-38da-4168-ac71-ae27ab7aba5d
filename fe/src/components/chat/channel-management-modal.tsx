import React, { useMemo } from 'react';
import { Setting<PERSON>, Hash, Lock } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import type { ChatChannelResponse } from '@/lib/types/api';

interface ChannelManagementModalProps {
  hubId: number;
  channel: ChatChannelResponse;
  isOpen: boolean;
  onClose: () => void;
}

export const ChannelManagementModal = React.memo<ChannelManagementModalProps>(({
  channel,
  isOpen,
  onClose
}) => {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();

  // Memoized computed values
  const isGeneralChannel = useMemo(() => channel.scope === 'general', [channel.scope]);

  // Memoized computed values
  const channelIcon = useMemo(() => {
    return isGeneralChannel ? <Hash className="h-5 w-5" /> : <Lock className="h-5 w-5" />;
  }, [isGeneralChannel]);

  const channelTypeLabel = useMemo(() => {
    return isGeneralChannel
      ? t(keys.collaborationHubs.chat.generalChannel)
      : t(keys.collaborationHubs.chat.customChannel);
  }, [isGeneralChannel, t, keys]);

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className={cn(
          "max-w-2xl w-full",
          isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0"
        )}>
          <DialogHeader className={cn(
            "pb-4",
            isMobile && "pb-2 pt-4 px-4"
          )}>
            <DialogTitle className={cn(
              "flex items-center gap-2",
              isMobile && "text-base"
            )}>
              <Settings className={cn("h-5 w-5", isMobile && "h-4 w-4")} />
              Channel Management
            </DialogTitle>
          </DialogHeader>

          <ScrollArea className={cn(
            "max-h-[60vh]",
            isMobile && "max-h-[calc(100vh-12rem)] px-4"
          )}>
            <div className="space-y-6 pr-4">
              {/* Section 1: Chat Settings */}
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  {channelIcon}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold truncate">
                      {channel.name}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {channelTypeLabel}
                    </p>
                  </div>
                </div>

                {channel.description && (
                  <p className="text-sm text-muted-foreground">
                    {channel.description}
                  </p>
                )}

                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>{channel.participant_count || 0} {t(keys.collaborationHubs.chat.participants)}</span>
                  {channel.created_at && (
                    <span>Created {new Date(channel.created_at).toLocaleDateString()}</span>
                  )}
                </div>
              </div>


            </div>
          </ScrollArea>

          {/* Close Button */}
          <div className={cn(
            "flex justify-end pt-4 border-t",
            isMobile && "px-4 pb-4"
          )}>
            <Button variant="outline" onClick={onClose}>
              {t(keys.common.close)}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
});

ChannelManagementModal.displayName = 'ChannelManagementModal';
