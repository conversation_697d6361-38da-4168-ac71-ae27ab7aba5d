import React, { useState, use<PERSON><PERSON>back, useMemo } from 'react';
import { MoreHorizontal, Edit, Trash2, Users } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useAuth } from '@/contexts/auth-context';
import { useDeleteMessage, useChatUtils } from '@/hooks/chat';
import { MessageContent } from './message-content';
import { MessageAttachments } from './message-attachments';
import { cn } from '@/lib/utils';
import { flushSync } from 'react-dom';

interface MessageItemProps {
  message: {
    id?: number;
    content?: string;
    sender?: {
      id?: number;
      name?: string;
      email?: string;
      role?: string;
      is_external?: boolean;
    };
    mentions?: Array<{
      name: string;
      email?: string;
    }>;
    attachments?: Array<{
      url: string;
      filename: string;
      content_type?: string;
      size: number;
      type?: string;
    }>;
    created_at?: string;
    updated_at?: string;
    edited_at?: string;
    is_edited?: boolean;
  };
  channelId: number;
  hubId: number;
  onEdit?: (messageId: number, content: string, attachments?: Array<{ url: string; filename: string; content_type?: string; size: number; type?: string }>) => void;
  className?: string;
}

export const MessageItem = React.memo<MessageItemProps>(({ message, channelId, hubId, onEdit, className }) => {
  const { t, keys } = useTranslations();
  const { user } = useAuth();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const { getInitials, getRoleBadgeColor } = useChatUtils();

  const deleteMessageMutation = useDeleteMessage();

  // Check if this is a system message
  const isSystemMessage = useMemo(() => {
    return message.message_type && message.message_type !== 'USER';
  }, [message]);

  // Memoized values to prevent unnecessary re-computations
  const isOwnMessage = useMemo(() =>
    user?.email === message.sender?.email,
    [user?.email, message.sender?.email]
  );

  const displayName = useMemo(() =>
    message.sender?.name || 'Unknown',
    [message.sender?.name]
  );

  const formattedTime = useMemo(() =>
    message.created_at ? new Date(message.created_at).toLocaleTimeString() : '',
    [message.created_at]
  );

  // Memoized event handlers to prevent unnecessary re-renders
  const handleDelete = useCallback(async () => {
    if (!message.id) return;

    try {
      await deleteMessageMutation.mutateAsync({
        params: { path: { channelId, messageId: message.id } }
      });
      setShowDeleteDialog(false);
    } catch (_error) {
      // Error handling for message deletion
    }
  }, [message.id, deleteMessageMutation, channelId]);

  const handleEdit = useCallback(
    (e?: React.MouseEvent) => {
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }

      if (!message.id || !message.content) return;

      console.log(
        "MessageItem handleEdit called - dropdown open state:",
        dropdownOpen
      );

      // Close dropdown immediately so React flushes the state update
      flushSync(() => setDropdownOpen(false));

      // Run the edit callback right after state flushes
      queueMicrotask(() => {
        onEdit?.(message.id!, message.content!, message.attachments || []);
      });
    },
    [message.id, message.content, message.attachments, onEdit, dropdownOpen]
  );

  const handleDeleteDialogOpen = useCallback(() => {
    setShowDeleteDialog(true);
  }, []);

  const handleDeleteDialogClose = useCallback((open: boolean) => {
    setShowDeleteDialog(open);
  }, []);

  // Safety check for message data
  if (!message || !message.id) {
    return null;
  }

  // For system messages, sender might be null, so we handle it differently
  if (isSystemMessage) {
    return (
      <div className={cn("flex justify-center py-3", className)}>
        <div className="flex items-center gap-3 text-sm text-muted-foreground bg-gradient-to-r from-muted/30 via-muted/50 to-muted/30 px-4 py-2.5 rounded-full border border-border/30 shadow-sm backdrop-blur-sm">
          <Users className="w-3.5 h-3.5 text-muted-foreground/70" />
          <span className="font-medium text-foreground/80">
            {message.content}
          </span>
        </div>
      </div>
    );
  }

  // Safety check for regular messages - they must have a sender
  if (!message.sender) {
    return null;
  }

  // Component render for regular messages
  return (
    <>
      <div className={cn("flex gap-3 group hover:bg-muted/50 p-2 rounded-lg transition-colors", className)}>
        <Avatar className="h-8 w-8 shrink-0">
          <AvatarImage src={undefined} />
          <AvatarFallback className="text-xs">
            {getInitials(displayName)}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 space-y-1 min-w-0">
          <div className="flex items-center gap-2 flex-wrap">
            <span className="font-medium text-sm">{displayName}</span>
            <Badge className={getRoleBadgeColor(message.sender?.role)}>
              {message.sender?.role?.replace('_', ' ') || 'User'}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {formattedTime}
            </span>
            {message.is_edited && (
              <span className="text-xs text-muted-foreground">
                ({t(keys.collaborationHubs.chat.edited)})
              </span>
            )}
          </div>

          <div className="text-sm">
            <MessageContent
              content={message.content || ''}
              mentions={message.mentions}
            />
          </div>

          <MessageAttachments hubId={hubId} attachments={message.attachments || []} />
        </div>

        {/* Message Actions - Only for regular user messages */}
        {isOwnMessage && (
          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
            <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
              <DropdownMenuTrigger>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  {t(keys.collaborationHubs.chat.editMessage)}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleDeleteDialogOpen}
                  className="text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {t(keys.collaborationHubs.chat.deleteMessage)}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={handleDeleteDialogClose}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t(keys.collaborationHubs.chat.confirmDelete)}</AlertDialogTitle>
            <AlertDialogDescription>
              {t(keys.collaborationHubs.chat.deleteDescription)}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t(keys.common.cancel)}</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              disabled={deleteMessageMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteMessageMutation.isPending ? t(keys.common.deleting) : t(keys.common.delete)}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
});

MessageItem.displayName = 'MessageItem';
