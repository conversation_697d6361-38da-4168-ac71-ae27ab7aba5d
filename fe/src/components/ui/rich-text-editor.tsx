import React, { useCallback, useEffect, useMemo, useRef } from 'react'
import { EditorContent, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { sanitizeHtml, normalizeLinkAttrs } from '@/lib/sanitize-html'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

export interface RichTextEditorProps {
  value?: string
  onChange?: (value: string) => void
  disabled?: boolean
  placeholder?: string
  className?: string
}

/**
 * Production-grade Rich Text Editor based on Tiptap (ProseMirror).
 * - Emits sanitized HTML (limited tags) for backend storage
 * - Minimal, accessible toolbar
 * - Optimized to reduce unnecessary re-renders
 */
const RichTextEditor: React.FC<RichTextEditorProps> = React.memo(({ value = '', onChange, disabled, placeholder, className }) => {
  const extensions = useMemo(() => [
    StarterKit.configure({
      heading: { levels: [1, 2, 3, 4, 5, 6] },
      // Configure the built-in Link extension from StarterKit
      link: {
        openOnClick: false,
        HTMLAttributes: { rel: 'noopener noreferrer', target: '_blank' },
        autolink: true,
        linkOnPaste: true,
      },
      // Configure the built-in Underline extension from StarterKit
      underline: {},
    }),
    // Note: Link and Underline are now included in StarterKit v3, so we don't need to add them separately
  ], [])

  const lastHtmlRef = useRef<string>(value || '<p></p>')

  const editor = useEditor({
    extensions,
    editable: !disabled,
    content: value || '<p></p>',
    injectCSS: false,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      const normalized = normalizeLinkAttrs(html)
      const clean = sanitizeHtml(normalized)
      lastHtmlRef.current = clean
      onChange?.(clean)
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-sm max-w-none focus:outline-none min-h-[140px] px-3 py-2',
          'dark:prose-invert',
        ),
      },
    },
  }, [extensions, disabled])
  // Re-render on any transaction (needed to reflect storedMarks changes like Ctrl+B on empty selection)
  const [, forceRerender] = React.useReducer((x) => x + 1, 0)
  useEffect(() => {
    if (!editor) return
    const handler = () => forceRerender()
    editor.on('transaction', handler)
    return () => editor.off('transaction', handler)
  }, [editor])


  // Keep external changes in sync (e.g., when editing existing brief)
  useEffect(() => {
    if (!editor) return
    const target = value || '<p></p>'
    if (target !== lastHtmlRef.current) {
      editor.commands.setContent(target)
      lastHtmlRef.current = target
    }
  }, [editor, value])

  const setLink = useCallback(() => {
    if (!editor) return
    const previousUrl = editor.getAttributes('link')?.href
    const url = window.prompt('Enter a URL', previousUrl || '') || ''
    if (url === '') {
      editor.chain().focus(undefined, { scrollIntoView: false }).extendMarkRange('link').unsetLink().run()
      return
    }
    try {
      const norm = new URL(url)
      const href = norm.toString()
      const { empty } = editor.state.selection
      const prev = editor.getHTML()
      if (empty) {
        const ok = editor.chain().focus(undefined, { scrollIntoView: false }).insertContent(`<a href="${href}" target="_blank" rel="noopener noreferrer">${href}</a>`).run()
        if (!ok || editor.getHTML() === prev) {
          editor.commands.setContent(`<p><a href="${href}" target="_blank" rel="noopener noreferrer">${href}</a></p>`, true)
        }
      } else {
        const ok = editor.chain().focus(undefined, { scrollIntoView: false }).extendMarkRange('link').setLink({ href, target: '_blank', rel: 'noopener noreferrer' }).run()
        if (!ok || editor.getHTML() === prev) {
          // minimal fallback: wrap all into link
          const text = editor.getText() || href
          editor.commands.setContent(`<p><a href="${href}" target="_blank" rel="noopener noreferrer">${text}</a></p>`, true)
        }
      }
    } catch {
      // Ignore invalid URL
    }
  }, [editor])

  const ToolbarButton: React.FC<{
    onClick: () => void
    active?: boolean
    disabled?: boolean
    children: React.ReactNode
    title?: string
  }> = ({ onClick, active, disabled, children, title }) => (
    <Button
      type="button"
      variant={active ? 'secondary' : 'ghost'}
      size="sm"
      onClick={(e) => {
        e.preventDefault()
        e.stopPropagation()
        onClick()
      }}
      onMouseDown={(e) => {
        // Prevent default to avoid focus issues that can cause double-click behavior
        e.preventDefault()
      }}
      disabled={disabled}
      title={title}
      aria-label={title}
      aria-pressed={active}
      className={cn('h-8 px-2')}
    >
      {children}
    </Button>
  )

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!editor) return
    const key = e.key.toLowerCase()
    const isMod = e.ctrlKey || e.metaKey
    if (isMod && key === 'b') {
      e.preventDefault()
      editor.chain().focus(undefined, { scrollIntoView: false }).toggleBold().run()
    }
  }, [editor])
  const markActive = useCallback((name: string) => {
    if (!editor) return false
    if (editor.isActive(name)) return true
    const stored = editor.state.storedMarks
    return !!stored?.some(m => m.type.name === name)
  }, [editor])


  // Helpers for JSDOM-safe block transforms when selection is empty
  const extractParagraphs = useCallback((html: string): string[] => {
    const matches = html.match(/<p>(.*?)<\/p>/g)
    if (matches && matches.length) {
      return matches.map(m => m.replace(/^<p>/, '').replace(/<\/p>$/, ''))
    }
    // Fallback: strip outer tag if any
    const inner = html.replace(/^<([^>]+)>/, '').replace(/<\/[^>]+>$/, '')
    return [inner]
  }, [])

  const wrapAsList = useCallback((type: 'ul' | 'ol') => {
    if (!editor) return
    const html = editor.getHTML()
    const items = extractParagraphs(html)
    const li = items.map(i => `<li>${i}</li>`).join('')
    editor.commands.setContent(`<${type}>${li}</${type}>`, true)
  }, [editor, extractParagraphs])

  const setHeadingLevelFallback = useCallback((level: 1|2|3|4|5|6) => {
    if (!editor) return
    const text = editor.getText() || ''
    editor.commands.setContent(`<h${level}>${text}</h${level}>`, true)
  }, [editor])


  return (
    <div className={cn('border rounded-md', disabled && 'opacity-70 pointer-events-none', className)} onKeyDown={handleKeyDown}>
      {/* Toolbar */}
      <div className="flex items-center gap-1 px-2 py-1 border-b bg-muted/50">
        {/* Text Type Dropdown */}
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <button
              type="button"
              aria-label="Text type"
              className={cn(
                // Use buttonVariants for consistent styling without ref warnings
                // ghost + sm
                'inline-flex items-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*=\'size-\'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50 rounded-md gap-1.5 has-[>svg]:px-2.5 h-8 px-2 min-w-[7.5rem] justify-between'
              )}
            >
              <span className="truncate text-left">
                {editor?.isActive('paragraph') && 'Normal text'}
                {editor?.isActive('heading', { level: 1 }) && 'Heading 1'}
                {editor?.isActive('heading', { level: 2 }) && 'Heading 2'}
                {editor?.isActive('heading', { level: 3 }) && 'Heading 3'}
                {editor?.isActive('heading', { level: 4 }) && 'Heading 4'}
                {editor?.isActive('heading', { level: 5 }) && 'Heading 5'}
                {editor?.isActive('heading', { level: 6 }) && 'Heading 6'}
                {!editor?.isActive('paragraph') &&
                  !editor?.isActive('heading') && 'Normal text'}
              </span>
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[14rem]" onMouseDown={(e) => e.preventDefault()}>
            <DropdownMenuRadioGroup
              value={
                editor?.isActive('paragraph')
                  ? 'paragraph'
                  : (editor?.getAttributes('heading')?.level?.toString() ?? '')
              }
              onValueChange={(val) => {
                if (!editor) return
                const prev = editor.getHTML()
                let ok = false
                if (val === 'paragraph') {
                  ok = editor.chain().focus(undefined, { scrollIntoView: false }).setParagraph().run()
                } else {
                  const level = Number(val) as 1|2|3|4|5|6
                  ok = editor.chain().focus(undefined, { scrollIntoView: false }).toggleHeading({ level }).run()
                  if ((!ok || editor.getHTML() === prev) && editor.state.selection.empty) {
                    setHeadingLevelFallback(level)
                  }
                }
              }}
            >
              <DropdownMenuRadioItem value="paragraph" aria-label="Normal text">
                <span className="text-sm">Normal text</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="1" aria-label="Heading 1">
                <span className="text-2xl font-bold">Heading 1</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="2" aria-label="Heading 2">
                <span className="text-xl font-semibold">Heading 2</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="3" aria-label="Heading 3">
                <span className="text-lg font-semibold">Heading 3</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="4" aria-label="Heading 4">
                <span className="text-base font-semibold">Heading 4</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="5" aria-label="Heading 5">
                <span className="text-sm font-semibold">Heading 5</span>
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="6" aria-label="Heading 6">
                <span className="text-xs font-semibold">Heading 6</span>
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        <Separator orientation="vertical" className="mx-1 h-6" />

        <ToolbarButton
          onClick={() => {
            if (!editor) return
            // Use storedMarks for bold on empty selection so aria-pressed updates correctly
            editor.chain().focus(undefined, { scrollIntoView: false }).toggleBold().run()
            // Force a re-render because storedMarks updates may not emit transactions in tests
            forceRerender()
          }}
          active={markActive('bold')}
          disabled={!editor?.can().chain().focus().toggleBold().run()}
          title="Bold"
        >
          <strong>B</strong>
        </ToolbarButton>
        <ToolbarButton
          onClick={() => {
            if (!editor) return
            editor.chain().focus(undefined, { scrollIntoView: false }).toggleItalic().run()
            forceRerender()
          }}
          active={markActive('italic')}
          disabled={!editor?.can().chain().focus().toggleItalic().run()}
          title="Italic"
        >
          <em>I</em>
        </ToolbarButton>
        <ToolbarButton
          onClick={() => {
            if (!editor) return
            editor.chain().focus(undefined, { scrollIntoView: false }).toggleUnderline().run()
            forceRerender()
          }}
          active={markActive('underline')}
          disabled={!editor?.can().chain().focus().toggleUnderline().run()}
          title="Underline"
        >
          <u>U</u>
        </ToolbarButton>

        <Separator orientation="vertical" className="mx-1 h-6" />

        <ToolbarButton
          onClick={() => {
            if (!editor) return
            // First try the standard toggle command
            if (editor.isActive('bulletList')) {
              // If already in a bullet list, toggle it off
              editor.chain().focus().toggleBulletList().run()
            } else {
              // If not in a bullet list, try to toggle it on
              const success = editor.chain().focus().toggleBulletList().run()
              // If the toggle didn't work (e.g., empty selection), use fallback
              if (!success && editor.state.selection.empty) {
                wrapAsList('ul')
              }
            }
            forceRerender()
          }}
          active={!!editor?.isActive('bulletList')}
          disabled={!editor}
          title="Bulleted list"
        >
          • • •
        </ToolbarButton>
        <ToolbarButton
          onClick={() => {
            if (!editor) return
            // First try the standard toggle command
            if (editor.isActive('orderedList')) {
              // If already in an ordered list, toggle it off
              editor.chain().focus().toggleOrderedList().run()
            } else {
              // If not in an ordered list, try to toggle it on
              const success = editor.chain().focus().toggleOrderedList().run()
              // If the toggle didn't work (e.g., empty selection), use fallback
              if (!success && editor.state.selection.empty) {
                wrapAsList('ol')
              }
            }
            forceRerender()
          }}
          active={!!editor?.isActive('orderedList')}
          disabled={!editor}
          title="Numbered list"
        >
          1.2.3.
        </ToolbarButton>

        <Separator orientation="vertical" className="mx-1 h-6" />

        <ToolbarButton onClick={setLink} active={!!editor?.isActive('link')} disabled={!editor} title="Insert link">Link</ToolbarButton>
      </div>

      {/* Editor */}
      <div className="px-2 py-1">
        {placeholder && !value && (
          <div className="pointer-events-none select-none text-muted-foreground text-sm px-3 pt-2">
            {placeholder}
          </div>
        )}
        <EditorContent editor={editor} role="textbox" aria-label="Rich text editor" />
      </div>
    </div>
  )
})

RichTextEditor.displayName = 'RichTextEditor'
export default RichTextEditor

