import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import RichTextEditor from '../rich-text-editor'

function setup(initialValue = '<p>Hello</p>') {
  const onChange = vi.fn()
  render(<RichTextEditor value={initialValue} onChange={onChange} />)
  const editor = screen.getByRole('textbox') as HTMLElement
  return { onChange, editor, user: userEvent.setup() }
}

describe('RichTextEditor toolbar', () => {
  beforeEach(() => {
    // Reset prompt
    ;(window as Window & { prompt?: unknown }).prompt = undefined
  })

  it('applies bulleted list', async () => {
    const { user, onChange } = setup()
    const bulletBtn = screen.getByRole('button', { name: /bulleted list/i })
    await waitFor(() => expect(bulletBtn).not.toBeDisabled())
    await user.click(bulletBtn)
    await waitFor(() => expect(onChange.mock.lastCall?.[0]).toBeDefined())
    const html = onChange.mock.lastCall?.[0] as string
    expect(html).toContain('<ul>')
    expect(html).toContain('<li>')
  })

  it('applies ordered list', async () => {
    const { user, onChange } = setup()
    const orderedBtn = screen.getByRole('button', { name: /numbered list/i })
    await waitFor(() => expect(orderedBtn).not.toBeDisabled())
    await user.click(orderedBtn)
    await waitFor(() => expect(onChange.mock.lastCall?.[0]).toBeDefined())
    const html = onChange.mock.lastCall?.[0] as string
    expect(html).toContain('<ol>')
    expect(html).toContain('<li>')
  })

  it('applies headings H1/H2/H3 and paragraph via dropdown', async () => {
    const { user, onChange } = setup()

    const textTypeTrigger = screen.getByRole('button', { name: /text type/i })
    await user.click(textTypeTrigger)

    const h1Item = await screen.findByRole('menuitemradio', { name: /heading 1/i })
    await user.click(h1Item)
    await waitFor(() => expect(onChange.mock.lastCall?.[0]).toBeDefined())
    let html = onChange.mock.lastCall?.[0] as string
    expect(html).toContain('<h1>')

    await user.click(textTypeTrigger)
    const h2Item = await screen.findByRole('menuitemradio', { name: /heading 2/i })
    await user.click(h2Item)
    await waitFor(() => expect(onChange.mock.lastCall?.[0]).toBeDefined())
    html = onChange.mock.lastCall?.[0] as string
    expect(html).toContain('<h2>')

    await user.click(textTypeTrigger)
    const h3Item = await screen.findByRole('menuitemradio', { name: /heading 3/i })
    await user.click(h3Item)
    await waitFor(() => expect(onChange.mock.lastCall?.[0]).toBeDefined())
    html = onChange.mock.lastCall?.[0] as string
    expect(html).toContain('<h3>')

    await user.click(textTypeTrigger)
    const normalItem = await screen.findByRole('menuitemradio', { name: /normal text/i })
    await user.click(normalItem)
    await waitFor(() => expect(onChange.mock.lastCall?.[0]).toBeDefined())
    html = onChange.mock.lastCall?.[0] as string
    expect(html).toContain('<p>')
  })

  it('inserts link when no selection using prompt', async () => {
    const { user, onChange } = setup('<p>Link here</p>')
    ;(window as Window & { prompt?: unknown }).prompt = vi.fn().mockReturnValue('https://example.com')

    const link = screen.getByRole('button', { name: /^insert link$/i })
    await waitFor(() => expect(link).not.toBeDisabled())
    await user.click(link)

    await waitFor(() => expect(onChange.mock.lastCall?.[0]).toBeDefined())
    const html = onChange.mock.lastCall?.[0] as string
    expect(html).toContain('<a')
    expect(html).toMatch(/href="https:\/\/example\.com/)
  })

  it('bold via toolbar toggles bold state (aria-pressed)', async () => {
    const { user } = setup('')
    const boldBtn = screen.getByRole('button', { name: /bold/i })
    await waitFor(() => expect(boldBtn).not.toBeDisabled())
    await user.click(boldBtn)
    expect(boldBtn).toHaveAttribute('aria-pressed', 'true')
  })
})

