import { useState, useCallback, useRef, useEffect } from 'react';
import { AlertCircle, RefreshCw, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useMediaUrlRefresh } from '@/hooks/media/use-media-url-refresh';
import { cn } from '@/lib/utils';

interface MediaItem {
  url: string;
  type: 'image' | 'video';
  alt?: string;
}

interface MediaCarouselProps {
  media: MediaItem[];
  hubId: number;
  postId?: number;
  className?: string;
  enableRetry?: boolean;
  onImageClick?: () => void;
  /** Controls image display mode - 'cover' crops to fit, 'contain' shows full image with black letterboxing */
  imageDisplayMode?: 'cover' | 'contain';
  /** Controls aspect ratio behavior - true forces square, false uses natural dimensions */
  forceAspectRatio?: boolean;
  /** Enables dynamic height calculation based on image aspect ratios for Facebook/Instagram-style behavior */
  dynamicHeight?: boolean;
  /** Target width for dynamic height calculation (defaults to 500px) */
  targetWidth?: number;
}

/**
 * Production-grade media carousel component with optional error recovery.
 * <PERSON><PERSON> presigned URL expiration gracefully when retry is enabled.
 */
export function MediaCarousel({
  media,
  hubId,
  postId,
  className = '',
  enableRetry = false,
  onImageClick,
  imageDisplayMode = 'cover',
  forceAspectRatio = true,
  dynamicHeight = false,
  targetWidth = 500
}: MediaCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [errors, setErrors] = useState<Map<number, string>>(new Map());
  const [retrying, setRetrying] = useState<Map<number, boolean>>(new Map());
  const [retryCounts, setRetryCounts] = useState<Map<number, number>>(new Map());
  const { handleMediaError, verifyAndRefreshUrl } = useMediaUrlRefresh();
  const retryTimeouts = useRef<NodeJS.Timeout[]>([]);
  const maxRetries = 3;

  // Dynamic height calculation state
  const [dimensionsLoaded, setDimensionsLoaded] = useState(false);
  const [calculatedHeight, setCalculatedHeight] = useState<number | null>(null);

  // Touch/swipe state for mobile navigation
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [touchStartY, setTouchStartY] = useState<number | null>(null);
  const [isSwiping, setIsSwiping] = useState(false);
  const [isVerticalScroll, setIsVerticalScroll] = useState(false);

  // Minimum swipe distance (in px) to trigger navigation - optimized for mobile
  const minSwipeDistance = 40;
  // Maximum vertical movement allowed for horizontal swipe detection
  const maxVerticalMovement = 30;

  // Cleanup timeouts on unmount
  useEffect(() => {
    const timeouts = retryTimeouts.current;
    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  // Dynamic height calculation effect
  useEffect(() => {
    if (!dynamicHeight || media.length === 0) {
      setDimensionsLoaded(true);
      return;
    }

    const loadImageDimensions = async () => {
      const dimensions = new Map<number, { width: number; height: number }>();
      const promises = media.map((item, index) => {
        return new Promise<void>((resolve) => {
          if (item.type === 'video') {
            // For videos, use a default aspect ratio or try to get video dimensions
            const video = document.createElement('video');
            video.onloadedmetadata = () => {
              dimensions.set(index, { width: video.videoWidth || 16, height: video.videoHeight || 9 });
              resolve();
            };
            video.onerror = () => {
              // Fallback to 16:9 aspect ratio for videos
              dimensions.set(index, { width: 16, height: 9 });
              resolve();
            };
            video.src = item.url;
          } else {
            // For images
            const img = new Image();
            img.onload = () => {
              dimensions.set(index, { width: img.naturalWidth, height: img.naturalHeight });
              resolve();
            };
            img.onerror = () => {
              // Fallback to square aspect ratio
              dimensions.set(index, { width: 1, height: 1 });
              resolve();
            };
            img.src = item.url;
          }
        });
      });

      try {
        await Promise.all(promises);

        // Calculate optimal height based on the tallest aspect ratio
        let minAspectRatio = Infinity;
        dimensions.forEach(({ width, height }) => {
          const aspectRatio = width / height;
          if (aspectRatio < minAspectRatio) {
            minAspectRatio = aspectRatio;
          }
        });

        // Calculate height: height = width / aspectRatio
        // Use min aspect ratio to accommodate the tallest (most portrait) image
        const optimalHeight = minAspectRatio !== Infinity ? targetWidth / minAspectRatio : 400;

        // Apply reasonable bounds
        const boundedHeight = Math.max(200, Math.min(800, optimalHeight));
        setCalculatedHeight(boundedHeight);
        setDimensionsLoaded(true);
      } catch (_error) {
        // Fallback to default height
        setCalculatedHeight(400);
        setDimensionsLoaded(true);
      }
    };

    loadImageDimensions();
  }, [dynamicHeight, media, targetWidth]);

  const handleError = useCallback(async (
    event: React.SyntheticEvent<HTMLImageElement | HTMLVideoElement>,
    index: number
  ) => {
    if (!enableRetry) return;

    const currentRetryCount = retryCounts.get(index) || 0;
    
    // Handle the error through our media refresh system
    await handleMediaError(event.nativeEvent, hubId, postId);
    
    if (currentRetryCount < maxRetries) {
      setRetrying(prev => new Map(prev).set(index, true));
      
      // Exponential backoff: 1s, 2s, 4s
      const delay = Math.pow(2, currentRetryCount) * 1000;
      
      const timeout = setTimeout(async () => {
        try {
          // Verify URL is accessible before retrying
          const isAccessible = await verifyAndRefreshUrl(media[index].url, hubId, postId);
          
          if (isAccessible || currentRetryCount === maxRetries - 1) {
            // Clear error and increment retry count
            setErrors(prev => {
              const newErrors = new Map(prev);
              newErrors.delete(index);
              return newErrors;
            });
            setRetryCounts(prev => new Map(prev).set(index, currentRetryCount + 1));
          } else {
            setRetryCounts(prev => new Map(prev).set(index, currentRetryCount + 1));
          }
        } catch (__error) {
          // Error handling for media retry
          setRetryCounts(prev => new Map(prev).set(index, currentRetryCount + 1));
        } finally {
          setRetrying(prev => {
            const newRetrying = new Map(prev);
            newRetrying.delete(index);
            return newRetrying;
          });
        }
      }, delay);
      
      retryTimeouts.current.push(timeout);
    } else {
      setErrors(prev => new Map(prev).set(index, 'Failed to load media. The URL may have expired.'));
    }
  }, [enableRetry, retryCounts, handleMediaError, verifyAndRefreshUrl, hubId, postId, media]);

  const handleManualRetry = useCallback((index: number) => {
    if (!enableRetry) return;

    setErrors(prev => {
      const newErrors = new Map(prev);
      newErrors.delete(index);
      return newErrors;
    });
    setRetryCounts(prev => new Map(prev).set(index, 0));
    setRetrying(prev => new Map(prev).set(index, true));
    
    // Force immediate refresh of post data
    handleMediaError(new Event('error'), hubId, postId).then(() => {
      setRetrying(prev => {
        const newRetrying = new Map(prev);
        newRetrying.delete(index);
        return newRetrying;
      });
    });
  }, [enableRetry, handleMediaError, hubId, postId]);

  const handleLoadSuccess = useCallback((index: number) => {
    setErrors(prev => {
      const newErrors = new Map(prev);
      newErrors.delete(index);
      return newErrors;
    });
    setRetryCounts(prev => {
      const newRetryCounts = new Map(prev);
      newRetryCounts.delete(index);
      return newRetryCounts;
    });
    setRetrying(prev => {
      const newRetrying = new Map(prev);
      newRetrying.delete(index);
      return newRetrying;
    });
  }, []);

  const nextSlide = useCallback(() => {
    setCurrentIndex(prev => (prev + 1) % media.length);
  }, [media.length]);

  const prevSlide = useCallback(() => {
    setCurrentIndex(prev => (prev - 1 + media.length) % media.length);
  }, [media.length]);

  // Enhanced touch event handlers for swipe navigation with scroll conflict prevention
  const onTouchStart = useCallback((e: React.TouchEvent) => {
    const touch = e.targetTouches[0];
    setTouchEnd(null);
    setTouchStart(touch.clientX);
    setTouchStartY(touch.clientY);
    setIsSwiping(false);
    setIsVerticalScroll(false);
  }, []);

  const onTouchMove = useCallback((e: React.TouchEvent) => {
    const touch = e.targetTouches[0];
    setTouchEnd(touch.clientX);

    if (touchStart !== null && touchStartY !== null) {
      const horizontalDistance = Math.abs(touch.clientX - touchStart);
      const verticalDistance = Math.abs(touch.clientY - touchStartY);

      // Determine if this is a vertical scroll gesture
      if (verticalDistance > maxVerticalMovement && verticalDistance > horizontalDistance) {
        setIsVerticalScroll(true);
        return; // Allow page scrolling
      }

      // If horizontal movement is significant and not vertical scrolling, consider it a swipe
      if (horizontalDistance > 10 && !isVerticalScroll) {
        setIsSwiping(true);
        // Prevent page scrolling during horizontal swipe
        e.preventDefault();
      }
    }
  }, [touchStart, touchStartY, maxVerticalMovement, isVerticalScroll]);

  const onTouchEnd = useCallback(() => {
    if (!touchStart || !touchEnd || isVerticalScroll) {
      // Reset states and allow normal behavior for vertical scrolls
      setTouchStart(null);
      setTouchEnd(null);
      setTouchStartY(null);
      setIsVerticalScroll(false);
      setTimeout(() => setIsSwiping(false), 100);
      return;
    }

    const horizontalDistance = touchStart - touchEnd;
    const isLeftSwipe = horizontalDistance > minSwipeDistance;
    const isRightSwipe = horizontalDistance < -minSwipeDistance;

    // Only navigate if we have multiple media items and a clear horizontal swipe
    if (media.length > 1) {
      if (isLeftSwipe) {
        nextSlide();
      } else if (isRightSwipe) {
        prevSlide();
      }
    }

    // Reset all touch states
    setTouchStart(null);
    setTouchEnd(null);
    setTouchStartY(null);
    setIsVerticalScroll(false);
    // Reset swiping state after a short delay to prevent immediate click
    setTimeout(() => setIsSwiping(false), 100);
  }, [touchStart, touchEnd, isVerticalScroll, media.length, nextSlide, prevSlide, minSwipeDistance]);

  // Handle image click (only if not swiping)
  const handleImageClick = useCallback(() => {
    if (!isSwiping && onImageClick) {
      onImageClick();
    }
  }, [isSwiping, onImageClick]);

  // Handle navigation area clicks (left/right edges)
  const handleNavigationClick = useCallback((direction: 'prev' | 'next', event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering image click
    if (media.length <= 1) return;

    if (direction === 'prev') {
      prevSlide();
    } else {
      nextSlide();
    }
  }, [media.length, prevSlide, nextSlide]);

  if (!media || media.length === 0) {
    return null;
  }

  const renderMediaItem = (item: MediaItem, index: number) => {
    const error = errors.get(index);
    const isRetrying = retrying.get(index);

    if (error && !isRetrying && enableRetry) {
      return (
        <div className="flex flex-col items-center justify-center p-8 bg-muted rounded-lg h-full">
          <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-sm text-muted-foreground text-center mb-4">{error}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleManualRetry(index)}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Retry
          </Button>
        </div>
      );
    }

    if (isRetrying && enableRetry) {
      const retryCount = retryCounts.get(index) || 0;
      return (
        <div className="flex flex-col items-center justify-center p-8 bg-muted rounded-lg h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
          <p className="text-sm text-muted-foreground">
            Refreshing media... (Attempt {retryCount + 1}/{maxRetries})
          </p>
        </div>
      );
    }

    // Social media style display with black letterboxing/pillarboxing for 'contain' mode
    if (imageDisplayMode === 'contain') {
      if (item.type === 'video') {
        return (
          <div className="w-full h-full bg-black rounded-lg flex items-center justify-center">
            <video
              src={item.url}
              className="max-w-full max-h-full object-contain"
              controls
              onError={(e) => handleError(e, index)}
              onLoadedData={() => handleLoadSuccess(index)}
              onClick={handleImageClick}
              preload="metadata"
            >
              Your browser does not support the video tag.
            </video>
          </div>
        );
      }

      return (
        <div className="w-full h-full bg-black rounded-lg flex items-center justify-center">
          <img
            src={item.url}
            alt={item.alt}
            className="max-w-full max-h-full object-contain cursor-pointer"
            onError={(e) => handleError(e, index)}
            onLoad={() => handleLoadSuccess(index)}
            onClick={handleImageClick}
            loading="lazy"
          />
        </div>
      );
    }

    // Standard cover mode (crops to fit)
    const objectFitClass = 'object-cover';

    if (item.type === 'video') {
      return (
        <video
          src={item.url}
          className={cn("w-full h-full rounded-lg", objectFitClass)}
          controls
          onError={(e) => handleError(e, index)}
          onLoadedData={() => handleLoadSuccess(index)}
          onClick={handleImageClick}
          preload="metadata"
        >
          Your browser does not support the video tag.
        </video>
      );
    }

    return (
      <img
        src={item.url}
        alt={item.alt}
        className={cn("w-full h-full rounded-lg cursor-pointer", objectFitClass)}
        onError={(e) => handleError(e, index)}
        onLoad={() => handleLoadSuccess(index)}
        onClick={handleImageClick}
        loading="lazy"
      />
    );
  };

  // Show loading state while calculating dimensions for dynamic height
  if (dynamicHeight && !dimensionsLoaded) {
    return (
      <div className={cn("relative flex items-center justify-center bg-muted rounded-lg", className)} style={{ height: 400 }}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Single media item
  if (media.length === 1) {
    const containerStyle = dynamicHeight && calculatedHeight
      ? { height: calculatedHeight, touchAction: 'pan-y' as const }
      : { touchAction: 'pan-y' as const };

    return (
      <div
        className={cn("relative", className)}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
        style={containerStyle}
        onClick={handleImageClick}
      >
        <div className={cn(!dynamicHeight && forceAspectRatio && "aspect-square", "h-full")}>
          {renderMediaItem(media[0], 0)}
        </div>
      </div>
    );
  }

  // Multiple media items - carousel
  const carouselContainerStyle = dynamicHeight && calculatedHeight
    ? { height: calculatedHeight, touchAction: 'pan-y' as const }
    : { touchAction: 'pan-y' as const };

  return (
    <div className={cn("relative", className)}>
      {/* Main media display */}
      <div
        className="relative overflow-hidden rounded-lg"
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
        style={carouselContainerStyle}
      >
        <div
          className="flex transition-transform duration-300 ease-in-out h-full"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {media.map((item, index) => (
            <div key={index} className="w-full flex-shrink-0 h-full">
              <div className={cn(!dynamicHeight && forceAspectRatio && "aspect-square", "h-full")}>
                {renderMediaItem(item, index)}
              </div>
            </div>
          ))}
        </div>

        {/* Expanded clickable navigation areas */}
        {media.length > 1 && (
          <>
            {/* Left navigation area (25% width) */}
            <div
              className="absolute left-0 top-0 w-1/4 h-full z-10 cursor-pointer group/nav-left"
              onClick={(e) => handleNavigationClick('prev', e)}
              aria-label="Previous image"
            >
              {/* Subtle hover feedback for desktop with gradient effect */}
              <div className="w-full h-full bg-gradient-to-r from-black/0 via-black/0 to-transparent group-hover/nav-left:from-black/5 group-hover/nav-left:via-black/3 transition-all duration-300 ease-out" />
            </div>

            {/* Right navigation area (25% width) */}
            <div
              className="absolute right-0 top-0 w-1/4 h-full z-10 cursor-pointer group/nav-right"
              onClick={(e) => handleNavigationClick('next', e)}
              aria-label="Next image"
            >
              {/* Subtle hover feedback for desktop with gradient effect */}
              <div className="w-full h-full bg-gradient-to-l from-black/0 via-black/0 to-transparent group-hover/nav-right:from-black/5 group-hover/nav-right:via-black/3 transition-all duration-300 ease-out" />
            </div>
          </>
        )}

        {/* Navigation arrows (visual indicators) */}
        {media.length > 1 && (
          <>
            <Button
              variant="outline"
              size="icon"
              className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white z-20 pointer-events-none"
              aria-hidden="true"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white z-20 pointer-events-none"
              aria-hidden="true"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </>
        )}

        {/* Dots indicator */}
        {media.length > 1 && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {media.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-white' : 'bg-white/50'
                }`}
                onClick={() => setCurrentIndex(index)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Media counter */}
      {media.length > 1 && (
        <div className="absolute top-4 right-4 bg-black/50 text-white px-2 py-1 rounded text-sm">
          {currentIndex + 1} / {media.length}
        </div>
      )}
    </div>
  );
}
