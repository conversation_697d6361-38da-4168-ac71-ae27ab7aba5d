import React from 'react';
import { Tag, Mail, Phone, MapPin, Globe, Building, FileText, Edit, Loader2, AlertCircle, Users, MoreHorizontal, Trash2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useBrand } from '@/hooks/brands';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useIsMobile } from '@/hooks/use-mobile';
import { usePermissions } from '@/hooks/use-permissions';
import { cn } from '@/lib/utils';
import type { Brand } from './types';

interface BrandViewDialogProps {
  brandId: number | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (brand: Brand) => void;
  onDelete?: (brand: Brand) => void;
}

export const BrandViewDialog = React.memo(function BrandViewDialog({
  brandId,
  open,
  onOpenChange,
  onEdit,
  onDelete
}: BrandViewDialogProps) {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();
  const { canManageBrands } = usePermissions();

  // Fetch brand data when dialog opens
  const {
    data: brand,
    isLoading,
    isError
  } = useBrand(brandId || 0, open && !!brandId);

  const handleEdit = React.useCallback(() => {
    if (brand && onEdit) {
      onEdit(brand);
    }
  }, [brand, onEdit]);

  const handleDelete = React.useCallback(() => {
    if (brand && onDelete) {
      onDelete(brand);
    }
  }, [brand, onDelete]);

  const formatAddress = React.useMemo(() => {
    if (!brand) return null;
    
    const parts = [
      brand.address_street,
      brand.address_city,
      brand.address_postal_code,
      brand.address_country
    ].filter(Boolean);
    
    return parts.length > 0 ? parts.join(', ') : null;
  }, [brand]);

  if (!open) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn(
        "max-w-6xl max-h-[90vh] w-full",
        isMobile && "max-w-[95vw] h-[95vh]"
      )}>
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="flex items-center gap-2">
            <Tag className="h-5 w-5 text-primary" />
            {isLoading ? (
              <span>Brand Details</span>
            ) : (
              <span>{brand?.name || 'Brand Details'}</span>
            )}
          </DialogTitle>

          {canManageBrands() && brand && !isLoading && (
            <DropdownMenu>
              <DropdownMenuTrigger>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  {t(keys.common.edit)}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleDelete}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {t(keys.common.delete)}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </DialogHeader>

        <ScrollArea className="flex-1 pr-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : isError ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Error loading brand data
              </AlertDescription>
            </Alert>
          ) : brand ? (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="h-4 w-4" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Brand Name
                      </label>
                      <p className="text-sm font-medium">{brand.name || '-'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Company Name
                      </label>
                      <p className="text-sm font-medium">{brand.company_name || '-'}</p>
                    </div>
                  </div>

                  {(brand.vat_number || brand.registration_number) && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          VAT Number
                        </label>
                        <p className="text-sm">{brand.vat_number || '-'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          Registration Number
                        </label>
                        <p className="text-sm">{brand.registration_number || '-'}</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Email</label>
                        {brand.email ? (
                          <a
                            href={`mailto:${brand.email}`}
                            className="text-sm hover:text-primary transition-colors block"
                          >
                            {brand.email}
                          </a>
                        ) : (
                          <p className="text-sm">-</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Phone</label>
                        {brand.phone ? (
                          <a
                            href={`tel:${brand.phone}`}
                            className="text-sm hover:text-primary transition-colors block"
                          >
                            {brand.phone}
                          </a>
                        ) : (
                          <p className="text-sm">-</p>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Website</label>
                      {brand.website ? (
                        <a
                          href={brand.website.startsWith('http') ? brand.website : `https://${brand.website}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm hover:text-primary transition-colors block"
                        >
                          {brand.website}
                        </a>
                      ) : (
                        <p className="text-sm">-</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Address</label>
                      {formatAddress ? (
                        <p className="text-sm">{formatAddress}</p>
                      ) : (
                        <p className="text-sm">-</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Brand Contacts */}
              {brand.contacts && brand.contacts.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Contacts ({brand.contacts.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {brand.contacts.map((contact, index) => (
                        <div key={contact.id || index}>
                          {index > 0 && <Separator className="my-3" />}
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-sm">{contact.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Mail className="h-3 w-3 text-muted-foreground" />
                              <a 
                                href={`mailto:${contact.email}`}
                                className="text-sm text-muted-foreground hover:text-primary transition-colors"
                              >
                                {contact.email}
                              </a>
                            </div>
                            {contact.notes && (
                              <p className="text-sm text-muted-foreground">{contact.notes}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Metadata */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Metadata
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <label className="text-muted-foreground">
                        Created At
                      </label>
                      <p>{new Date(brand.created_at).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <label className="text-muted-foreground">
                        Updated At
                      </label>
                      <p>{new Date(brand.updated_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : null}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
});
