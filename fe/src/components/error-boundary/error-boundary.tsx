import { Component, type ReactNode } from 'react';
import { ErrorFallback } from './error-fallback';
import { logError } from './error-logger';

export type ErrorBoundaryFallbackType = 'page' | 'component' | 'minimal';

interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
  errorBoundaryStack?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallbackType?: ErrorBoundaryFallbackType;
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
  showRefresh?: boolean;
  customFallback?: (error: Error, errorInfo: ErrorInfo, retry: () => void) => ReactNode;
  isolate?: boolean; // If true, prevents error from bubbling up
}

/**
 * Error Boundary component that catches JavaScript errors anywhere in the child component tree,
 * logs those errors, and displays a fallback UI instead of the component tree that crashed.
 * 
 * Features:
 * - Different fallback UI types (page, component, minimal)
 * - Error logging and reporting
 * - Refresh/retry functionality
 * - Custom fallback components
 * - Error isolation to prevent bubbling
 * - TypeScript support
 * - Integration with app's design system
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError, isolate } = this.props;
    const { errorId } = this.state;

    // Log the error
    logError(error, errorInfo, errorId || 'unknown');

    // Update state with error info
    this.setState({ errorInfo });

    // Call custom error handler if provided
    if (onError && errorId) {
      try {
        onError(error, errorInfo, errorId);
      } catch (handlerError) {
        console.error('Error in custom error handler:', handlerError);
      }
    }

    // Prevent error from bubbling up if isolate is true
    if (isolate) {
      // Error is contained within this boundary
      return;
    }

    // Log to external error reporting service if available
    if (typeof window !== 'undefined' && (window as any).errorReporter) {
      try {
        (window as any).errorReporter.captureException(error, {
          extra: {
            errorInfo,
            errorId,
            component: 'ErrorBoundary',
          },
        });
      } catch (reporterError) {
        console.error('Error reporting failed:', reporterError);
      }
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  handleRetry = () => {
    // Clear any existing timeout
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }

    // Reset error state
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  handleRefresh = () => {
    // For page-level errors, refresh the entire page
    if (this.props.fallbackType === 'page') {
      window.location.reload();
    } else {
      // For component-level errors, just retry the component
      this.handleRetry();
    }
  };

  render() {
    const { hasError, error, errorInfo } = this.state;
    const { children, fallbackType = 'component', customFallback, showRefresh = true } = this.props;

    if (hasError && error) {
      // Custom fallback takes precedence
      if (customFallback && errorInfo) {
        return customFallback(error, errorInfo, this.handleRetry);
      }

      // Use built-in fallback UI
      return (
        <ErrorFallback
          error={error}
          errorInfo={errorInfo}
          fallbackType={fallbackType}
          onRetry={this.handleRetry}
          onRefresh={this.handleRefresh}
          showRefresh={showRefresh}
        />
      );
    }

    return children;
  }
}

export default ErrorBoundary;
