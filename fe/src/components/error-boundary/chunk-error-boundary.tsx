import React, { Component, type ReactNode } from 'react';
import { ErrorFallback } from './error-fallback';
import { logError } from './error-logger';

interface ChunkErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  isChunkError: boolean;
}

interface ChunkErrorBoundaryProps {
  children: ReactNode;
  onChunkError?: () => void;
}

/**
 * Specialized error boundary for handling chunk loading errors.
 * These errors commonly occur in production when the app is updated
 * and users have stale JavaScript chunks cached.
 * 
 * Features:
 * - Detects chunk loading errors specifically
 * - Provides user-friendly messaging about app updates
 * - Automatically suggests page refresh to get latest chunks
 * - Logs chunk errors for monitoring
 */
export class ChunkErrorBoundary extends Component<ChunkErrorBoundaryProps, ChunkErrorBoundaryState> {
  constructor(props: ChunkErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      isChunkError: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ChunkErrorBoundaryState> {
    const isChunkError = ChunkErrorBoundary.isChunkLoadError(error);
    
    return {
      hasError: true,
      error,
      isChunkError,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const { onChunkError } = this.props;
    const isChunkError = ChunkErrorBoundary.isChunkLoadError(error);

    // Log the error with proper type conversion
    const errorInfoForLogging = {
      componentStack: errorInfo.componentStack || '',
      errorBoundary: errorInfo.errorBoundary,
      errorBoundaryStack: errorInfo.errorBoundaryStack,
    };

    logError(error, errorInfoForLogging, `chunk_error_${Date.now()}`, {
      isChunkError,
      errorType: 'chunk_loading',
    });

    // Handle chunk errors specifically
    if (isChunkError) {
      console.warn('Chunk loading error detected. This usually means the app has been updated.');
      
      // Call custom chunk error handler if provided
      if (onChunkError) {
        try {
          onChunkError();
        } catch (handlerError) {
          console.error('Error in chunk error handler:', handlerError);
        }
      }

      // Clear any cached chunks/modules if possible
      this.clearCachedChunks();
    }
  }

  /**
   * Determines if an error is related to chunk loading
   */
  static isChunkLoadError(error: Error): boolean {
    const chunkErrorPatterns = [
      /Loading chunk \d+ failed/,
      /ChunkLoadError/,
      /Loading CSS chunk/,
      /Failed to import/,
      /Cannot resolve module/,
      /Module not found/,
      /Unexpected token '<'/,  // Often indicates HTML returned instead of JS
      /SyntaxError.*Unexpected token/,
    ];

    const errorMessage = error.message || '';
    const errorName = error.name || '';
    const errorStack = error.stack || '';

    return chunkErrorPatterns.some(pattern => 
      pattern.test(errorMessage) || 
      pattern.test(errorName) || 
      pattern.test(errorStack)
    );
  }

  /**
   * Attempts to clear cached chunks and modules
   */
  private clearCachedChunks(): void {
    try {
      // Clear module cache if available (webpack specific)
      if (typeof window !== 'undefined' && (window as any).__webpack_require__) {
        const webpackRequire = (window as any).__webpack_require__;
        if (webpackRequire.cache) {
          Object.keys(webpackRequire.cache).forEach(key => {
            delete webpackRequire.cache[key];
          });
        }
      }

      // Clear any cached dynamic imports
      if ('caches' in window) {
        caches.keys().then(cacheNames => {
          cacheNames.forEach(cacheName => {
            if (cacheName.includes('js') || cacheName.includes('css')) {
              caches.delete(cacheName);
            }
          });
        }).catch(e => {
          console.warn('Failed to clear caches:', e);
        });
      }
    } catch (e) {
      console.warn('Failed to clear cached chunks:', e);
    }
  }

  /**
   * Handles page refresh for chunk errors
   */
  private handleRefresh = (): void => {
    // For chunk errors, we want to do a hard refresh to bypass cache
    window.location.reload();
  };

  render() {
    const { hasError, error } = this.state;
    const { children } = this.props;

    if (hasError && error) {
      return (
        <ErrorFallback
          error={error}
          errorInfo={null}
          fallbackType="page"
          onRetry={this.handleRefresh}
          onRefresh={this.handleRefresh}
          showRefresh={true}
        />
      );
    }

    return children;
  }
}

export default ChunkErrorBoundary;
