/**
 * Error Boundary components for the Collaboration Hub frontend.
 * 
 * Provides comprehensive error handling with different fallback UIs
 * and refresh functionality for pages and components.
 * 
 * Components:
 * - ErrorBoundary: Main error boundary with configurable fallback types
 * - PageErrorBoundary: Specialized wrapper for page-level errors
 * - ComponentErrorBoundary: Specialized wrapper for component-level errors
 * - ErrorFallback: Fallback UI components for different contexts
 * 
 * Utilities:
 * - logError: Error logging and reporting utilities
 * - getStoredErrorLogs: Retrieve stored error logs for debugging
 * - clearStoredErrorLogs: Clear stored error logs
 * - logWarning: Log warning-level errors
 * 
 * Usage:
 * 
 * // Page-level error boundary
 * <PageErrorBoundary pageName="Dashboard">
 *   <DashboardPage />
 * </PageErrorBoundary>
 * 
 * // Component-level error boundary
 * <ComponentErrorBoundary componentName="PostsList" isolate={true}>
 *   <PostsList />
 * </ComponentErrorBoundary>
 * 
 * // Custom error boundary
 * <ErrorBoundary 
 *   fallbackType="minimal" 
 *   onError={customErrorHandler}
 *   showRefresh={false}
 * >
 *   <SomeComponent />
 * </ErrorBoundary>
 */

export { ErrorBoundary } from './error-boundary';
export { PageErrorBoundary } from './page-error-boundary';
export { ComponentErrorBoundary } from './component-error-boundary';
export { ChunkErrorBoundary } from './chunk-error-boundary';
export { ErrorFallback } from './error-fallback';
export { 
  logError, 
  logWarning, 
  getStoredErrorLogs, 
  clearStoredErrorLogs 
} from './error-logger';

export type { ErrorBoundaryFallbackType } from './error-boundary';

// Re-export for convenience
export { ErrorBoundary as default };
