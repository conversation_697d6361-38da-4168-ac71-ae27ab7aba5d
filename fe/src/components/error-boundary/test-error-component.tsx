import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ComponentErrorBoundary } from './component-error-boundary';

/**
 * Test component that can throw errors on demand.
 * Used for testing error boundary functionality.
 */
function ErrorThrowingComponent() {
  const [shouldThrow, setShouldThrow] = useState(false);

  if (shouldThrow) {
    throw new Error('Test error thrown by ErrorThrowingComponent');
  }

  return (
    <div className="p-4 border rounded">
      <h3 className="text-lg font-semibold mb-2">Error Boundary Test Component</h3>
      <p className="text-sm text-muted-foreground mb-4">
        Click the button below to trigger an error and test the error boundary.
      </p>
      <Button 
        onClick={() => setShouldThrow(true)}
        variant="destructive"
      >
        Throw Error
      </Button>
    </div>
  );
}

/**
 * Test component wrapped with error boundary.
 * Use this to test error boundary functionality in development.
 */
export function TestErrorBoundary() {
  return (
    <ComponentErrorBoundary 
      componentName="TestErrorComponent" 
      isolate={true}
      fallbackType="component"
    >
      <ErrorThrowingComponent />
    </ComponentErrorBoundary>
  );
}

export default TestErrorBoundary;
