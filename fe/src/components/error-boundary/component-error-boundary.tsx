import { type ReactNode } from 'react';
import { ErrorBoundary } from './error-boundary';

interface ComponentErrorBoundaryProps {
  children: ReactNode;
  componentName?: string;
  fallbackType?: 'component' | 'minimal';
  isolate?: boolean;
  onError?: (error: Error, errorInfo: any, errorId: string) => void;
  showRefresh?: boolean;
}

/**
 * Specialized error boundary for component-level errors.
 * Provides inline error fallback UI with retry functionality.
 * 
 * Usage:
 * <ComponentErrorBoundary componentName="PostsList" isolate={true}>
 *   <PostsList />
 * </ComponentErrorBoundary>
 */
export function ComponentErrorBoundary({ 
  children, 
  componentName,
  fallbackType = 'component',
  isolate = true,
  onError,
  showRefresh = true
}: ComponentErrorBoundaryProps) {
  const handleError = (error: Error, errorInfo: any, errorId: string) => {
    // Add component context to error logging
    const enhancedErrorInfo = {
      ...errorInfo,
      componentName,
      errorBoundaryType: 'component',
    };

    // Call custom error handler if provided
    if (onError) {
      onError(error, enhancedErrorInfo, errorId);
    }

    // Log component-specific error details
    console.error(`Component Error Boundary [${componentName || 'Unknown'}]:`, {
      error,
      errorInfo: enhancedErrorInfo,
      errorId,
    });
  };

  return (
    <ErrorBoundary
      fallbackType={fallbackType}
      onError={handleError}
      showRefresh={showRefresh}
      isolate={isolate}
    >
      {children}
    </ErrorBoundary>
  );
}

export default ComponentErrorBoundary;
