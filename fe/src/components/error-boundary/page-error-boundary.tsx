import { type ReactNode } from 'react';
import { ErrorBoundary } from './error-boundary';

interface PageErrorBoundaryProps {
  children: ReactNode;
  pageName?: string;
  onError?: (error: Error, errorInfo: any, errorId: string) => void;
}

/**
 * Specialized error boundary for page-level components.
 * Provides full-page error fallback UI with refresh functionality.
 * 
 * Usage:
 * <PageErrorBoundary pageName="Dashboard">
 *   <DashboardPage />
 * </PageErrorBoundary>
 */
export function PageErrorBoundary({ 
  children, 
  pageName,
  onError 
}: PageErrorBoundaryProps) {
  const handleError = (error: Error, errorInfo: any, errorId: string) => {
    // Add page context to error logging
    const enhancedErrorInfo = {
      ...errorInfo,
      pageName,
      errorBoundaryType: 'page',
    };

    // Call custom error handler if provided
    if (onError) {
      onError(error, enhancedErrorInfo, errorId);
    }

    // Log page-specific error details
    console.error(`Page Error Boundary [${pageName || 'Unknown'}]:`, {
      error,
      errorInfo: enhancedErrorInfo,
      errorId,
    });
  };

  return (
    <ErrorBoundary
      fallbackType="page"
      onError={handleError}
      showRefresh={true}
      isolate={false} // Allow page errors to bubble up to app level
    >
      {children}
    </ErrorBoundary>
  );
}

export default PageErrorBoundary;
