# Error Boundary System

A comprehensive error boundary system for the Collaboration Hub frontend that provides graceful error handling with refresh functionality.

## Overview

The error boundary system consists of multiple layers of error handling:

1. **App-level Error Boundary** - Catches critical application errors
2. **Chunk Error Boundary** - Handles chunk loading errors (production builds)
3. **Page-level Error Boundaries** - Isolate page-specific errors
4. **Component-level Error Boundaries** - Isolate component-specific errors

## Components

### `ErrorBoundary`
Main error boundary component with configurable fallback types.

```tsx
<ErrorBoundary 
  fallbackType="page" // 'page' | 'component' | 'minimal'
  onError={(error, errorInfo, errorId) => {
    // Custom error handling
  }}
  showRefresh={true}
  isolate={false}
>
  <YourComponent />
</ErrorBoundary>
```

### `PageErrorBoundary`
Specialized wrapper for page-level components with full-page error UI.

```tsx
<PageErrorBoundary pageName="Dashboard">
  <DashboardPage />
</PageErrorBoundary>
```

### `ComponentErrorBoundary`
Wrapper for component-level errors with inline error UI.

```tsx
<ComponentErrorBoundary 
  componentName="PostsList" 
  isolate={true}
  fallbackType="component"
>
  <PostsList />
</ComponentErrorBoundary>
```

### `ChunkErrorBoundary`
Handles chunk loading errors common in production builds.

```tsx
<ChunkErrorBoundary
  onChunkError={() => {
    console.warn('App updated - refresh required');
  }}
>
  <App />
</ChunkErrorBoundary>
```

## Error Fallback Types

### Page Fallback
- Full-screen error UI
- Refresh page button
- Navigation to dashboard
- Suitable for page-level errors

### Component Fallback
- Inline error UI within component bounds
- Retry component button
- Technical details in development
- Suitable for component-level errors

### Minimal Fallback
- Compact error message
- Simple retry button
- Minimal visual impact
- Suitable for small components

## Error Logging

All errors are automatically logged with:
- Error details and stack trace
- Component context information
- User and account context
- Timestamp and error ID
- Browser and environment info

### Local Storage
- Last 10 errors stored locally for debugging
- Accessible via `getStoredErrorLogs()`
- Can be cleared with `clearStoredErrorLogs()`

### External Reporting
- Integrates with error reporting services (Sentry, LogRocket, etc.)
- Custom error endpoint support via environment variables
- Automatic error context enrichment

## Implementation

### Current Coverage

**App Level:**
- Main App component wrapped with ChunkErrorBoundary and ErrorBoundary

**Page Level:**
- All main pages wrapped with PageErrorBoundary:
  - Dashboard
  - Collaboration Hubs
  - Collaboration Hub Detail
  - Invoices
  - Brands
  - Account Companies
  - Bank Details
  - Settings
  - Notifications

**Component Level:**
- Complex components wrapped with ComponentErrorBoundary:
  - PostsList (infinite scroll, API calls)
  - MessageList (WebSocket, real-time features)
  - MediaUpload (file handling, drag-and-drop)
  - InvoiceViewDialog (PDF generation, complex forms)

### Adding Error Boundaries

To add error boundaries to new components:

1. **For Pages:**
```tsx
import { PageErrorBoundary } from '@/components/error-boundary';

// In router configuration
{
  path: "new-page",
  element: (
    <PageErrorBoundary pageName="New Page">
      <NewPage />
    </PageErrorBoundary>
  )
}
```

2. **For Components:**
```tsx
import { ComponentErrorBoundary } from '@/components/error-boundary';

// Wrap the component export
export function MyComponent(props: MyComponentProps) {
  return (
    <ComponentErrorBoundary 
      componentName="MyComponent" 
      isolate={true}
    >
      <MyComponentInternal {...props} />
    </ComponentErrorBoundary>
  );
}
```

## Best Practices

1. **Isolate Complex Components** - Wrap components with complex state, API calls, or real-time features
2. **Use Appropriate Fallback Types** - Page fallbacks for pages, component fallbacks for components
3. **Provide Context** - Always include component/page names for better debugging
4. **Handle Chunk Errors** - Use ChunkErrorBoundary at the app level for production builds
5. **Log Errors Appropriately** - Use custom error handlers for specific logging needs
6. **Test Error Scenarios** - Regularly test error boundaries in development

## Development vs Production

### Development
- Shows detailed error information
- Includes stack traces and component stacks
- Provides technical details for debugging
- Console logging is verbose

### Production
- User-friendly error messages
- Hides technical details from users
- Focuses on recovery actions (refresh, retry)
- Logs errors to external services

## Environment Variables

```env
# Optional: Custom error reporting endpoint
VITE_ERROR_REPORTING_ENDPOINT=https://your-error-service.com/api/errors

# Optional: App version for error context
VITE_APP_VERSION=1.0.0
```

## Troubleshooting

### Common Issues

1. **Chunk Loading Errors** - Usually indicates app update, refresh required
2. **Component Isolation** - Set `isolate={true}` to prevent error bubbling
3. **Missing Translations** - Ensure error translation keys are added
4. **Memory Leaks** - Error boundaries clean up timeouts and subscriptions

### Debugging

1. Check browser console for detailed error logs
2. Use `getStoredErrorLogs()` to view recent errors
3. Check network tab for failed chunk requests
4. Verify error boundary placement in component tree
