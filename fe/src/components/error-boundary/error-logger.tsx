/**
 * Error logging utilities for the error boundary system.
 * Provides centralized error logging with different levels and contexts.
 */

interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
  errorBoundaryStack?: string;
}

interface ErrorContext {
  userId?: string;
  accountId?: string;
  route?: string;
  userAgent?: string;
  timestamp: string;
  errorId: string;
  buildVersion?: string;
}

interface ErrorLogEntry {
  error: {
    name: string;
    message: string;
    stack?: string;
  };
  errorInfo: ErrorInfo;
  context: ErrorContext;
  level: 'error' | 'warning' | 'info';
}

/**
 * Logs errors to console and external services
 */
export function logError(
  error: Error, 
  errorInfo: ErrorInfo, 
  errorId: string,
  additionalContext?: Record<string, any>
): void {
  const context: ErrorContext = {
    errorId,
    timestamp: new Date().toISOString(),
    route: window.location.pathname,
    userAgent: navigator.userAgent,
    buildVersion: import.meta.env.VITE_APP_VERSION || 'unknown',
    ...additionalContext,
  };

  // Try to get user context from auth
  try {
    const authData = localStorage.getItem('auth-storage');
    if (authData) {
      const parsed = JSON.parse(authData);
      if (parsed.state?.user) {
        context.userId = parsed.state.user.id?.toString();
        context.accountId = parsed.state.user.accountId?.toString();
      }
    }
  } catch (e) {
    // Ignore auth context errors
  }

  const logEntry: ErrorLogEntry = {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    errorInfo,
    context,
    level: 'error',
  };

  // Console logging (always enabled)
  console.group(`🚨 Error Boundary Caught Error [${errorId}]`);
  console.error('Error:', error);
  console.error('Error Info:', errorInfo);
  console.error('Context:', context);
  console.error('Full Log Entry:', logEntry);
  console.groupEnd();

  // Development-specific logging
  if (import.meta.env.DEV) {
    console.warn('Error Boundary - Development Mode');
    console.warn('Component Stack:', errorInfo.componentStack);
    if (error.stack) {
      console.warn('Error Stack:', error.stack);
    }
  }

  // Send to external error reporting service
  sendToErrorReporter(logEntry);

  // Store in local storage for debugging (keep last 10 errors)
  storeErrorLocally(logEntry);
}

/**
 * Sends error to external error reporting service
 */
function sendToErrorReporter(logEntry: ErrorLogEntry): void {
  try {
    // Check if error reporter is available (e.g., Sentry, LogRocket, etc.)
    if (typeof window !== 'undefined' && (window as any).errorReporter) {
      (window as any).errorReporter.captureException(new Error(logEntry.error.message), {
        tags: {
          errorBoundary: true,
          errorId: logEntry.context.errorId,
          route: logEntry.context.route,
        },
        extra: {
          errorInfo: logEntry.errorInfo,
          context: logEntry.context,
          originalError: logEntry.error,
        },
        level: logEntry.level,
      });
    }

    // Alternative: Send to custom error endpoint
    if (import.meta.env.VITE_ERROR_REPORTING_ENDPOINT) {
      fetch(import.meta.env.VITE_ERROR_REPORTING_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(logEntry),
      }).catch((fetchError) => {
        console.warn('Failed to send error to reporting endpoint:', fetchError);
      });
    }
  } catch (reportingError) {
    console.warn('Error reporting failed:', reportingError);
  }
}

/**
 * Stores error in local storage for debugging
 */
function storeErrorLocally(logEntry: ErrorLogEntry): void {
  try {
    const storageKey = 'error-boundary-logs';
    const existingLogs = JSON.parse(localStorage.getItem(storageKey) || '[]');
    
    // Keep only the last 10 errors
    const updatedLogs = [logEntry, ...existingLogs].slice(0, 10);
    
    localStorage.setItem(storageKey, JSON.stringify(updatedLogs));
  } catch (storageError) {
    console.warn('Failed to store error locally:', storageError);
  }
}

/**
 * Retrieves stored error logs for debugging
 */
export function getStoredErrorLogs(): ErrorLogEntry[] {
  try {
    const storageKey = 'error-boundary-logs';
    return JSON.parse(localStorage.getItem(storageKey) || '[]');
  } catch (e) {
    return [];
  }
}

/**
 * Clears stored error logs
 */
export function clearStoredErrorLogs(): void {
  try {
    localStorage.removeItem('error-boundary-logs');
  } catch (e) {
    console.warn('Failed to clear error logs:', e);
  }
}

/**
 * Logs a warning-level error (for non-critical errors)
 */
export function logWarning(
  message: string,
  context?: Record<string, any>
): void {
  const warningEntry = {
    error: {
      name: 'Warning',
      message,
    },
    errorInfo: {
      componentStack: 'N/A',
    },
    context: {
      errorId: `warning_${Date.now()}`,
      timestamp: new Date().toISOString(),
      route: window.location.pathname,
      ...context,
    },
    level: 'warning' as const,
  };

  console.warn('Error Boundary Warning:', warningEntry);
  
  // Store warnings locally too
  storeErrorLocally(warningEntry);
}

export default logError;
