import { <PERSON>ert<PERSON><PERSON>gle, <PERSON>fresh<PERSON><PERSON>, RotateCcw, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/router/routes';
import type { ErrorBoundaryFallbackType } from './error-boundary';

interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
  errorBoundaryStack?: string;
}

interface ErrorFallbackProps {
  error: Error;
  errorInfo: ErrorInfo | null;
  fallbackType: ErrorBoundaryFallbackType;
  onRetry: () => void;
  onRefresh: () => void;
  showRefresh: boolean;
}

/**
 * Error fallback UI component that displays different error interfaces
 * based on the context (page, component, or minimal).
 */
export function ErrorFallback({
  error,
  fallbackType,
  onRetry,
  onRefresh,
  showRefresh,
}: ErrorFallbackProps) {
  const { t, keys } = useTranslations();
  const navigate = useNavigate();

  const isChunkError = error.message.includes('Loading chunk') || 
                      error.message.includes('ChunkLoadError') ||
                      error.name === 'ChunkLoadError';

  const isDevelopment = import.meta.env.DEV;

  const handleGoHome = () => {
    navigate(ROUTES.DASHBOARD);
  };

  // Page-level error fallback (full screen)
  if (fallbackType === 'page') {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-background">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
              <AlertTriangle className="h-6 w-6 text-destructive" />
            </div>
            <CardTitle className="text-xl">
              {isChunkError 
                ? t(keys.errors.chunkError.title)
                : t(keys.errors.pageError.title)
              }
            </CardTitle>
            <CardDescription>
              {isChunkError 
                ? t(keys.errors.chunkError.description)
                : t(keys.errors.pageError.description)
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {isDevelopment && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-sm font-mono">
                  <div className="font-semibold mb-1">{error.name}: {error.message}</div>
                  {error.stack && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-xs opacity-80">Stack trace</summary>
                      <pre className="mt-1 text-xs whitespace-pre-wrap opacity-70">
                        {error.stack}
                      </pre>
                    </details>
                  )}
                </AlertDescription>
              </Alert>
            )}
            
            <div className="flex flex-col gap-2">
              {showRefresh && (
                <Button onClick={onRefresh} className="w-full">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  {isChunkError 
                    ? t(keys.errors.actions.reload)
                    : t(keys.errors.actions.refresh)
                  }
                </Button>
              )}
              
              <Button variant="outline" onClick={handleGoHome} className="w-full">
                <Home className="mr-2 h-4 w-4" />
                {t(keys.errors.actions.goHome)}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Component-level error fallback (inline)
  if (fallbackType === 'component') {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-5 w-5 text-destructive mt-0.5" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-sm mb-1">
                {t(keys.errors.componentError.title)}
              </h3>
              <p className="text-sm text-muted-foreground mb-3">
                {t(keys.errors.componentError.description)}
              </p>
              
              {isDevelopment && (
                <details className="mb-3">
                  <summary className="cursor-pointer text-xs text-muted-foreground hover:text-foreground">
                    Technical details
                  </summary>
                  <div className="mt-2 p-2 bg-muted rounded text-xs font-mono">
                    <div className="font-semibold">{error.name}: {error.message}</div>
                    {error.stack && (
                      <pre className="mt-1 whitespace-pre-wrap opacity-70 max-h-32 overflow-y-auto">
                        {error.stack.split('\n').slice(0, 5).join('\n')}
                      </pre>
                    )}
                  </div>
                </details>
              )}
              
              <div className="flex gap-2">
                <Button size="sm" onClick={onRetry}>
                  <RotateCcw className="mr-1 h-3 w-3" />
                  {t(keys.errors.actions.retry)}
                </Button>
                {showRefresh && (
                  <Button size="sm" variant="outline" onClick={onRefresh}>
                    <RefreshCw className="mr-1 h-3 w-3" />
                    {t(keys.errors.actions.refresh)}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Minimal error fallback (compact)
  return (
    <Alert variant="destructive" className="my-2">
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <span className="text-sm">
          {t(keys.errors.minimal.message)}
        </span>
        <Button size="sm" variant="outline" onClick={onRetry} className="ml-2">
          <RotateCcw className="mr-1 h-3 w-3" />
          {t(keys.errors.actions.retry)}
        </Button>
      </AlertDescription>
    </Alert>
  );
}

export default ErrorFallback;
