import { RouterProvider } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ThemeProvider } from '@/components/theme-provider';
import { AuthProvider } from '@/contexts/auth-context';
import { WebSocketProvider } from '@/lib/websocket';
import { Toaster } from '@/components/ui/sonner';
import { router } from '@/router/router';
import { queryClient } from '@/lib/query-client';
import { ErrorBoundary } from '@/components/error-boundary';
import { ChunkErrorBoundary } from '@/components/error-boundary/chunk-error-boundary';

function App() {
  return (
    <ChunkErrorBoundary
      onChunkError={() => {
        console.warn('Chunk loading error detected - app may have been updated');
      }}
    >
      <ErrorBoundary
        fallbackType="page"
        onError={(error, errorInfo, errorId) => {
          console.error('App-level error caught:', { error, errorInfo, errorId });
        }}
      >
        <QueryClientProvider client={queryClient}>
          <ThemeProvider defaultTheme="dark" storageKey="theme">
            <AuthProvider>
              <WebSocketProvider>
                <RouterProvider router={router} />
                <Toaster />
              </WebSocketProvider>
            </AuthProvider>
          </ThemeProvider>
          {/* React Query Devtools - only shows in development */}
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </ErrorBoundary>
    </ChunkErrorBoundary>
  );
}

export default App;
