
import { useState } from "react"
import { useSearchParams } from "react-router"
import { Building2, Plus, AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CardSkeletonGrid } from "@/components/ui/card-skeleton"
import { ResponsiveCardGrid } from "@/components/ui/responsive-card-grid"
import { CompanyCard } from '@/components/account-company/company-card.tsx';
import { EditCompanyDialog } from "@/components/account-company/edit-company-dialog.tsx"
import { DeleteCompanyDialog } from "@/components/account-company/delete-company-dialog.tsx"
import { CompanyViewDialog } from "@/components/account-company/company-view-dialog.tsx"
import { useAccountCompanies } from "@/hooks/account-companies"
import { usePermissions } from "@/hooks/use-permissions"
import { useTranslations } from '@/lib/i18n/typed-translations'
import type { AccountCompany } from '@/components/account-company/types';
import { DeepLinkUtils } from '@/lib/notification-navigation';

export default function AccountCompaniesPage() {
  const [searchParams, setSearchParams] = useSearchParams()
  const [editingCompany, setEditingCompany] = useState<AccountCompany | null>(null)
  const [deletingCompany, setDeletingCompany] = useState<AccountCompany | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const { t, keys } = useTranslations()
  const { canManageCompanies } = usePermissions()

  // URL-driven view dialog state
  const viewCompanyId = DeepLinkUtils.getCompanyToView(searchParams)
  const viewDialogOpen = !!viewCompanyId

  const { data: companies, isLoading, error, isError, refetch } = useAccountCompanies()

  const handleCreateNew = () => {
    setIsCreating(true)
    setEditingCompany(null)
  }

  const handleEdit = (company: AccountCompany) => {
    setIsCreating(false)
    setEditingCompany(company)
  }

  const handleDelete = (company: AccountCompany) => {
    setDeletingCompany(company)
  }

  const handleView = (company: AccountCompany) => {
    const newParams = DeepLinkUtils.updateSearchParams(searchParams, {
      viewCompany: company.id.toString(),
    })
    setSearchParams(newParams, { replace: true })
  }

  const handleViewDialogClose = (open: boolean) => {
    if (!open) {
      const newParams = DeepLinkUtils.updateSearchParams(searchParams, {
        viewCompany: null,
      })
      setSearchParams(newParams, { replace: true })
    }
  }

  const handleViewDialogEdit = (company: AccountCompany) => {
    // Close view dialog and open edit dialog
    handleViewDialogClose(false)
    setEditingCompany(company)
    setIsCreating(false)
  }

  const handleViewDialogDelete = (company: AccountCompany) => {
    // Close view dialog and open delete dialog
    handleViewDialogClose(false)
    setDeletingCompany(company)
  }

  const handleCloseDialog = () => {
    setEditingCompany(null)
    setIsCreating(false)
  }

  const handleSuccess = () => {
    // Dialogs will close automatically via their onSuccess handlers
    // Data will be automatically refreshed via React Query cache invalidation
  }

  if (isLoading) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{t(keys.accountCompanies.title)}</h1>
            <p className="text-muted-foreground">{t(keys.accountCompanies.description)}</p>
          </div>
          {canManageCompanies() && (
            <Button onClick={handleCreateNew} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              {t(keys.accountCompanies.createNew)}
            </Button>
          )}
        </div>

        <CardSkeletonGrid count={6} />
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{t(keys.accountCompanies.title)}</h1>
            <p className="text-muted-foreground">{t(keys.accountCompanies.description)}</p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error?.error.code || t(keys.accountCompanies.error)}
          </AlertDescription>
        </Alert>

        <div className="flex justify-center">
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  const hasCompanies = companies && companies.length > 0

  return (
    <div className="flex flex-1 flex-col gap-4 p-4">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">{t('accountCompanies.title')}</h1>
          <p className="text-muted-foreground">{t('accountCompanies.description')}</p>
        </div>
        {canManageCompanies() && (
          <Button onClick={handleCreateNew} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            {t('accountCompanies.createNew')}
          </Button>
        )}
      </div>

      {hasCompanies ? (
        <ResponsiveCardGrid>
          {companies.map((company) => (
            <CompanyCard
              key={company.id}
              company={company}
              onEdit={() => handleEdit(company)}
              onDelete={() => handleDelete(company)}
              onView={() => handleView(company)}
            />
          ))}
        </ResponsiveCardGrid>
      ) : (
        <div className="flex flex-col items-center justify-center min-h-[500px] text-center max-w-2xl mx-auto px-4">
          <div className="mb-6 p-4 bg-muted rounded-full">
            <Building2 className="h-12 w-12 text-muted-foreground" />
          </div>
          <h2 className="text-2xl font-semibold mb-3">{t('accountCompanies.noCompanies')}</h2>
          <p className="text-muted-foreground text-lg mb-8 leading-relaxed">
            {t('accountCompanies.noCompaniesDescription')}
          </p>
          {canManageCompanies() && (
            <Button onClick={handleCreateNew} className="flex items-center gap-2" size="lg">
              <Plus className="h-5 w-5" />
              {t('accountCompanies.createNew')}
            </Button>
          )}
        </div>
      )}

      <EditCompanyDialog
        company={isCreating ? null : editingCompany}
        open={isCreating || !!editingCompany}
        onClose={handleCloseDialog}
        onSuccess={handleSuccess}
      />

      <DeleteCompanyDialog
        company={deletingCompany}
        open={!!deletingCompany}
        onClose={() => setDeletingCompany(null)}
        onSuccess={handleSuccess}
      />

      <CompanyViewDialog
        companyId={viewCompanyId}
        open={viewDialogOpen}
        onOpenChange={handleViewDialogClose}
        onEdit={handleViewDialogEdit}
        onDelete={handleViewDialogDelete}
      />
    </div>
  )
}